<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Dashboard
            'view dashboard',

            // User Management
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Role Management
            'view roles',
            'create roles',
            'edit roles',
            'delete roles',

            // Customer Management
            'view customers',
            'create customers',
            'edit customers',
            'delete customers',

            // Network Management
            'view network',
            'manage network devices',
            'manage network sites',
            'manage ip pools',

            // Bandwidth Management
            'view bandwidth',
            'manage bandwidth plans',
            'manage bandwidth policies',
            'manage bandwidth quotas',

            // Service Management
            'view services',
            'create services',
            'edit services',
            'delete services',
            'manage service types',

            // Subscription Management
            'view subscriptions',
            'create subscriptions',
            'edit subscriptions',
            'delete subscriptions',

            // Invoice Management
            'view invoices',
            'create invoices',
            'edit invoices',
            'delete invoices',

            // Payment Management
            'view payments',
            'process payments',
            'refund payments',

            // Settings Management
            'view admin settings',
            'manage system settings',
            'manage mpesa settings',

            // Reports
            'view reports',
            'export reports',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Super Admin - Full access
        $superAdmin = Role::create(['name' => 'Super Admin']);
        $superAdmin->givePermissionTo(Permission::all());

        // Admin - Most permissions except user/role management
        $admin = Role::create(['name' => 'Admin']);
        $admin->givePermissionTo([
            'view dashboard',
            'view customers', 'create customers', 'edit customers', 'delete customers',
            'view network', 'manage network devices', 'manage network sites', 'manage ip pools',
            'view bandwidth', 'manage bandwidth plans', 'manage bandwidth policies', 'manage bandwidth quotas',
            'view services', 'create services', 'edit services', 'delete services', 'manage service types',
            'view subscriptions', 'create subscriptions', 'edit subscriptions', 'delete subscriptions',
            'view invoices', 'create invoices', 'edit invoices', 'delete invoices',
            'view payments', 'process payments',
            'view admin settings', 'manage system settings', 'manage mpesa settings',
            'view reports', 'export reports',
        ]);

        // Manager - Customer and service management
        $manager = Role::create(['name' => 'Manager']);
        $manager->givePermissionTo([
            'view dashboard',
            'view customers', 'create customers', 'edit customers',
            'view services', 'create services', 'edit services',
            'view subscriptions', 'create subscriptions', 'edit subscriptions',
            'view invoices', 'create invoices', 'edit invoices',
            'view payments', 'process payments',
            'view reports',
        ]);

        // Technician - Network and technical management
        $technician = Role::create(['name' => 'Technician']);
        $technician->givePermissionTo([
            'view dashboard',
            'view customers', 'edit customers',
            'view network', 'manage network devices', 'manage network sites', 'manage ip pools',
            'view bandwidth', 'manage bandwidth plans', 'manage bandwidth policies',
            'view services', 'edit services',
            'view subscriptions', 'edit subscriptions',
        ]);

        // Operator - Basic operations
        $operator = Role::create(['name' => 'Operator']);
        $operator->givePermissionTo([
            'view dashboard',
            'view customers', 'edit customers',
            'view services',
            'view subscriptions',
            'view invoices',
            'view payments',
        ]);

        // Viewer - Read-only access
        $viewer = Role::create(['name' => 'Viewer']);
        $viewer->givePermissionTo([
            'view dashboard',
            'view customers',
            'view services',
            'view subscriptions',
            'view invoices',
            'view payments',
            'view reports',
        ]);
    }
}
