<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('static_ip_services', function (Blueprint $table) {
            // Provisioning status tracking fields
            $table->enum('provisioning_status', [
                'pending', 
                'provisioning', 
                'active', 
                'provisioning_failed', 
                'reactivation_failed'
            ])->default('pending')->after('status')
                ->comment('Real-time provisioning status for WebSocket updates');
            
            $table->string('provisioning_stage')->nullable()->after('provisioning_status')
                ->comment('Current provisioning step being executed');
            
            $table->integer('provisioning_progress')->default(0)->after('provisioning_stage')
                ->comment('Percentage completion (0-100)');
            
            $table->text('provisioning_error')->nullable()->after('provisioning_progress')
                ->comment('Detailed error message if provisioning failed');
            
            $table->timestamp('provisioning_started_at')->nullable()->after('provisioning_error')
                ->comment('When provisioning began');
            
            $table->timestamp('provisioning_completed_at')->nullable()->after('provisioning_started_at')
                ->comment('When provisioning finished successfully');
            
            $table->timestamp('last_provisioning_attempt')->nullable()->after('provisioning_completed_at')
                ->comment('For retry tracking and rate limiting');
            
            $table->integer('provisioning_attempts')->default(0)->after('last_provisioning_attempt')
                ->comment('Number of provisioning attempts made');
            
            // Add indexes for performance
            $table->index(['provisioning_status'], 'static_ip_provisioning_status_idx');
            $table->index(['provisioning_started_at'], 'static_ip_provisioning_started_idx');
            $table->index(['last_provisioning_attempt'], 'static_ip_last_attempt_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('static_ip_services', function (Blueprint $table) {
            $table->dropIndex('static_ip_provisioning_status_idx');
            $table->dropIndex('static_ip_provisioning_started_idx');
            $table->dropIndex('static_ip_last_attempt_idx');
            
            $table->dropColumn([
                'provisioning_status',
                'provisioning_stage',
                'provisioning_progress',
                'provisioning_error',
                'provisioning_started_at',
                'provisioning_completed_at',
                'last_provisioning_attempt',
                'provisioning_attempts'
            ]);
        });
    }
};
