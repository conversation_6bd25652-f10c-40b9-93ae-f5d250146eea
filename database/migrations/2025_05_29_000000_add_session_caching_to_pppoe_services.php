<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pppoe_services', function (Blueprint $table) {
            // Session caching fields for performance optimization
            $table->json('cached_session_data')->nullable()->after('comment')
                ->comment('Cached active session data from MikroTik API');
            $table->timestamp('session_data_cached_at')->nullable()->after('cached_session_data')
                ->comment('When the session data was last cached');
            $table->boolean('is_currently_connected')->default(false)->after('session_data_cached_at')
                ->comment('Quick lookup for connection status');
            $table->string('current_session_ip')->nullable()->after('is_currently_connected')
                ->comment('Current IP address from active session');
            $table->string('current_session_uptime')->nullable()->after('current_session_ip')
                ->comment('Current session uptime');
            $table->timestamp('last_session_check')->nullable()->after('current_session_uptime')
                ->comment('When we last checked for active session');
            
            // Add indexes for performance
            $table->index(['is_currently_connected'], 'pppoe_connection_status_idx');
            $table->index(['last_session_check'], 'pppoe_last_check_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pppoe_services', function (Blueprint $table) {
            $table->dropIndex('pppoe_connection_status_idx');
            $table->dropIndex('pppoe_last_check_idx');
            $table->dropColumn([
                'cached_session_data',
                'session_data_cached_at',
                'is_currently_connected',
                'current_session_ip',
                'current_session_uptime',
                'last_session_check'
            ]);
        });
    }
};
