<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add currency column to bandwidth_plans table
        Schema::table('bandwidth_plans', function (Blueprint $table) {
            $table->string('currency', 3)->default('KES')->after('price');
            $table->index('currency');
        });

        // Add currency columns to invoices table
        Schema::table('invoices', function (Blueprint $table) {
            $table->string('currency', 3)->default('KES')->after('total_amount');
            $table->index('currency');
        });

        // Add currency columns to payments table if it exists
        if (Schema::hasTable('payments')) {
            Schema::table('payments', function (Blueprint $table) {
                $table->string('currency', 3)->default('KES')->after('amount');
                $table->index('currency');
            });
        }

        // Insert default currency setting
        DB::table('system_settings')->insertOrIgnore([
            'key' => 'default_currency',
            'value' => 'KES',
            'description' => 'Default currency for the system (USD, KES, ETB, UGX)',
            'type' => 'select',
            'group' => 'currency',
            'is_public' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Insert currency display format settings
        DB::table('system_settings')->insertOrIgnore([
            'key' => 'currency_display_format',
            'value' => 'symbol_before',
            'description' => 'Currency display format (symbol_before, symbol_after, code_before, code_after)',
            'type' => 'select',
            'group' => 'currency',
            'is_public' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Insert decimal places setting
        DB::table('system_settings')->insertOrIgnore([
            'key' => 'currency_decimal_places',
            'value' => '2',
            'description' => 'Number of decimal places for currency display',
            'type' => 'integer',
            'group' => 'currency',
            'is_public' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove currency columns
        Schema::table('bandwidth_plans', function (Blueprint $table) {
            $table->dropIndex(['currency']);
            $table->dropColumn('currency');
        });

        Schema::table('invoices', function (Blueprint $table) {
            $table->dropIndex(['currency']);
            $table->dropColumn('currency');
        });

        if (Schema::hasTable('payments')) {
            Schema::table('payments', function (Blueprint $table) {
                $table->dropIndex(['currency']);
                $table->dropColumn('currency');
            });
        }

        // Remove currency settings
        DB::table('system_settings')->whereIn('key', [
            'default_currency',
            'currency_display_format',
            'currency_decimal_places'
        ])->delete();
    }
};
