import * as React from "react"
import { Check, ChevronsUpDown, User, Mail, Loader2, AlertCircle, RefreshCw } from "lucide-react"
import axios from "axios"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface Customer {
  id: number
  name: string
  email: string
  status?: string
}

interface CustomerSearchResponse {
  data: Customer[]
  total: number
  has_more: boolean
  page: number
}

interface CustomerComboboxProps {
  value?: string
  onValueChange?: (value: string) => void
  initialCustomers?: Customer[]  // For backward compatibility
  loading?: boolean
  disabled?: boolean
  placeholder?: string
  className?: string
  showStatus?: boolean
  filterActiveOnly?: boolean
  minSearchLength?: number
  debounceMs?: number
  pageSize?: number
  searchEndpoint?: string
}

export function CustomerCombobox({
  value,
  onValueChange,
  customers = [],
  loading = false,
  disabled = false,
  placeholder = "Select a customer",
  className,
  showStatus = false,
  filterActiveOnly = true,
}: CustomerComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState("")

  // Filter customers based on status and search input
  const filteredCustomers = React.useMemo(() => {
    let filtered = customers

    // Filter by status if needed
    if (filterActiveOnly) {
      filtered = filtered.filter(customer => customer.status === 'active' || !customer.status)
    }

    // Filter by search input
    if (searchValue) {
      filtered = filtered.filter(customer =>
        customer.name.toLowerCase().includes(searchValue.toLowerCase()) ||
        customer.email.toLowerCase().includes(searchValue.toLowerCase())
      )
    }

    return filtered
  }, [customers, searchValue, filterActiveOnly])

  // Get selected customer
  const selectedCustomer = React.useMemo(() => {
    if (!value) return null
    return customers.find(customer => customer.id.toString() === value)
  }, [customers, value])

  // Get display text for the trigger button
  const getDisplayText = () => {
    if (loading) return "Loading customers..."
    if (customers.length === 0) return "No customers available"
    if (selectedCustomer) return selectedCustomer.name
    return placeholder
  }

  // Get placeholder text for search input
  const getSearchPlaceholder = () => {
    if (customers.length === 0) return "No customers available"
    if (customers.length > 20) return "Search customers by name or email..."
    return "Type to filter customers..."
  }

  // Get status badge
  const getStatusBadge = (status?: string) => {
    if (!showStatus || !status) return null

    const statusColors = {
      active: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",
      inactive: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400",
      suspended: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
    }

    return (
      <span className={cn(
        "ml-2 text-xs px-2 py-1 rounded capitalize",
        statusColors[status as keyof typeof statusColors] || "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
      )}>
        {status}
      </span>
    )
  }

  return (
    <div className={cn("flex", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="flex-1 justify-between"
            disabled={disabled || loading || customers.length === 0}
          >
            <div className="flex items-center truncate">
              <User className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <span className={cn(
                "truncate",
                !selectedCustomer && "text-muted-foreground"
              )}>
                {getDisplayText()}
              </span>
            </div>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[400px] p-0" align="start">
          <Command shouldFilter={false}>
            <CommandInput
              placeholder={getSearchPlaceholder()}
              value={searchValue}
              onValueChange={setSearchValue}
              className="h-9"
            />
            <CommandList>
              <CommandEmpty>
                {searchValue
                  ? `No customers found matching "${searchValue}"`
                  : "No customers available"
                }
              </CommandEmpty>
              {filteredCustomers.length > 0 && (
                <CommandGroup>
                  {/* Show total count if there are many customers */}
                  {customers.length > 10 && (
                    <div className="px-2 py-1.5 text-xs text-muted-foreground border-b">
                      {filteredCustomers.length} of {customers.length} customers
                      {searchValue && ` matching "${searchValue}"`}
                      {filterActiveOnly && " (active only)"}
                    </div>
                  )}

                  {filteredCustomers.map((customer) => (
                    <CommandItem
                      key={customer.id}
                      value={customer.id.toString()}
                      onSelect={(currentValue) => {
                        onValueChange?.(currentValue === value ? "" : currentValue)
                        setOpen(false)
                        setSearchValue("")
                      }}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center min-w-0 flex-1">
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4 shrink-0",
                            value === customer.id.toString() ? "opacity-100" : "opacity-0"
                          )}
                        />
                        <div className="min-w-0 flex-1">
                          <div className="font-medium truncate">{customer.name}</div>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Mail className="mr-1 h-3 w-3 shrink-0" />
                            <span className="truncate">{customer.email}</span>
                          </div>
                        </div>
                      </div>

                      {/* Show status badge */}
                      {getStatusBadge(customer.status)}
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
