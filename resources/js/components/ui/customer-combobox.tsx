import * as React from "react"
import { Check, ChevronsUpDown, User, Mail, Loader2, AlertCircle, RefreshCw } from "lucide-react"
import axios from "axios"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface Customer {
  id: number
  name: string
  email: string
  status?: string
}

interface CustomerSearchResponse {
  data: Customer[]
  total: number
  has_more: boolean
  page: number
}

interface CustomerComboboxProps {
  value?: string
  onValueChange?: (value: string) => void
  initialCustomers?: Customer[]  // For backward compatibility
  loading?: boolean
  disabled?: boolean
  placeholder?: string
  className?: string
  showStatus?: boolean
  filterActiveOnly?: boolean
  minSearchLength?: number
  debounceMs?: number
  pageSize?: number
  searchEndpoint?: string
}

export function CustomerCombobox({
  value,
  onValueChange,
  initialCustomers = [],
  loading: externalLoading = false,
  disabled = false,
  placeholder = "Select a customer",
  className,
  showStatus = false,
  filterActiveOnly = true,
  minSearchLength = 2,
  debounceMs = 400,
  pageSize = 50,
  searchEndpoint = "/api/customers/search",
}: CustomerComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState("")
  const [customers, setCustomers] = React.useState<Customer[]>(initialCustomers)
  const [selectedCustomer, setSelectedCustomer] = React.useState<Customer | null>(null)
  const [isSearching, setIsSearching] = React.useState(false)
  const [hasSearched, setHasSearched] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)
  const [hasMore, setHasMore] = React.useState(false)
  const [currentPage, setCurrentPage] = React.useState(1)
  const [totalResults, setTotalResults] = React.useState(0)

  // Cache for search results
  const searchCache = React.useRef<Map<string, CustomerSearchResponse>>(new Map())

  // Debounced search function
  const debouncedSearch = React.useCallback(
    React.useMemo(() => {
      let timeoutId: NodeJS.Timeout
      return (query: string, page: number = 1) => {
        clearTimeout(timeoutId)
        timeoutId = setTimeout(() => {
          performSearch(query, page)
        }, debounceMs)
      }
    }, [debounceMs]),
    []
  )

  // Search function with caching
  const performSearch = React.useCallback(async (query: string, page: number = 1) => {
    if (query.length < minSearchLength) {
      setCustomers(initialCustomers)
      setHasSearched(false)
      setError(null)
      return
    }

    const cacheKey = `${query}-${page}-${filterActiveOnly}`

    // Check cache first
    if (searchCache.current.has(cacheKey)) {
      const cachedResult = searchCache.current.get(cacheKey)!
      if (page === 1) {
        setCustomers(cachedResult.data)
      } else {
        setCustomers(prev => [...prev, ...cachedResult.data])
      }
      setHasMore(cachedResult.has_more)
      setTotalResults(cachedResult.total)
      setCurrentPage(cachedResult.page)
      return
    }

    setIsSearching(true)
    setError(null)

    try {
      const response = await axios.get<CustomerSearchResponse>(searchEndpoint, {
        params: {
          q: query,
          page,
          per_page: pageSize,
          active_only: filterActiveOnly,
        },
      })

      const result = response.data

      // Cache the result
      searchCache.current.set(cacheKey, result)

      if (page === 1) {
        setCustomers(result.data)
      } else {
        setCustomers(prev => [...prev, ...result.data])
      }

      setHasMore(result.has_more)
      setTotalResults(result.total)
      setCurrentPage(result.page)
      setHasSearched(true)
    } catch (err) {
      console.error('Customer search failed:', err)
      setError('Failed to search customers. Please try again.')
      if (page === 1) {
        setCustomers(initialCustomers)
      }
    } finally {
      setIsSearching(false)
    }
  }, [searchEndpoint, pageSize, filterActiveOnly, minSearchLength, initialCustomers])

  // Load selected customer if value is provided but customer not found
  React.useEffect(() => {
    if (value && !selectedCustomer) {
      const found = customers.find(c => c.id.toString() === value) ||
                   initialCustomers.find(c => c.id.toString() === value)

      if (found) {
        setSelectedCustomer(found)
      } else if (value) {
        // Try to fetch the specific customer
        axios.get(`/api/customers/${value}`)
          .then(response => {
            setSelectedCustomer(response.data)
          })
          .catch(() => {
            // Customer not found, clear selection
            onValueChange?.("")
          })
      }
    } else if (!value) {
      setSelectedCustomer(null)
    }
  }, [value, customers, initialCustomers, selectedCustomer, onValueChange])

  // Handle search input changes
  const handleSearchChange = React.useCallback((newValue: string) => {
    setSearchValue(newValue)
    setCurrentPage(1)

    if (newValue.length >= minSearchLength) {
      debouncedSearch(newValue, 1)
    } else if (newValue.length === 0) {
      setCustomers(initialCustomers)
      setHasSearched(false)
      setError(null)
      setHasMore(false)
      setTotalResults(0)
    }
  }, [debouncedSearch, minSearchLength, initialCustomers])

  // Load more results (for virtual scrolling)
  const loadMore = React.useCallback(() => {
    if (hasMore && !isSearching && searchValue.length >= minSearchLength) {
      debouncedSearch(searchValue, currentPage + 1)
    }
  }, [hasMore, isSearching, searchValue, currentPage, debouncedSearch, minSearchLength])

  // Get display text for the trigger button
  const getDisplayText = () => {
    if (externalLoading || isSearching) return "Loading customers..."
    if (selectedCustomer) return selectedCustomer.name
    return placeholder
  }

  // Get placeholder text for search input
  const getSearchPlaceholder = () => {
    if (minSearchLength > 1) {
      return `Type at least ${minSearchLength} characters to search...`
    }
    return "Search customers by name or email..."
  }

  // Get empty state message
  const getEmptyMessage = () => {
    if (error) return error
    if (isSearching) return "Searching customers..."
    if (searchValue.length > 0 && searchValue.length < minSearchLength) {
      return `Type at least ${minSearchLength} characters to search`
    }
    if (hasSearched && customers.length === 0) {
      return `No customers found matching "${searchValue}"`
    }
    if (!hasSearched && initialCustomers.length === 0) {
      return "Start typing to search for customers"
    }
    return "No customers available"
  }

  // Get status badge
  const getStatusBadge = (status?: string) => {
    if (!showStatus || !status) return null

    const statusColors = {
      active: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",
      inactive: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400",
      suspended: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
    }

    return (
      <span className={cn(
        "ml-2 text-xs px-2 py-1 rounded capitalize",
        statusColors[status as keyof typeof statusColors] || "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
      )}>
        {status}
      </span>
    )
  }

  return (
    <div className={cn("flex", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="flex-1 justify-between"
            disabled={disabled || externalLoading}
          >
            <div className="flex items-center truncate">
              <User className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <span className={cn(
                "truncate",
                !selectedCustomer && "text-muted-foreground"
              )}>
                {getDisplayText()}
              </span>
            </div>
            <div className="flex items-center ml-2">
              {(isSearching || externalLoading) && (
                <Loader2 className="h-4 w-4 animate-spin mr-1" />
              )}
              <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[400px] p-0" align="start">
          <Command shouldFilter={false}>
            <div className="flex items-center border-b px-3">
              <CommandInput
                placeholder={getSearchPlaceholder()}
                value={searchValue}
                onValueChange={handleSearchChange}
                className="h-9 border-0 focus:ring-0"
              />
              {isSearching && (
                <Loader2 className="h-4 w-4 animate-spin ml-2 shrink-0" />
              )}
            </div>

            {/* Error state */}
            {error && (
              <div className="p-3">
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="flex items-center justify-between">
                    {error}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setError(null)
                        if (searchValue.length >= minSearchLength) {
                          performSearch(searchValue, 1)
                        }
                      }}
                    >
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Retry
                    </Button>
                  </AlertDescription>
                </Alert>
              </div>
            )}

            <CommandList className="max-h-[300px] overflow-auto">
              <CommandEmpty>
                {getEmptyMessage()}
              </CommandEmpty>

              {customers.length > 0 && (
                <CommandGroup>
                  {/* Show search results info */}
                  {hasSearched && (
                    <div className="px-2 py-1.5 text-xs text-muted-foreground border-b">
                      {totalResults > 0 ? (
                        <>
                          Showing {customers.length} of {totalResults} customers
                          {searchValue && ` matching "${searchValue}"`}
                          {filterActiveOnly && " (active only)"}
                        </>
                      ) : (
                        `No results for "${searchValue}"`
                      )}
                    </div>
                  )}

                  {customers.map((customer) => (
                    <CommandItem
                      key={customer.id}
                      value={customer.id.toString()}
                      onSelect={(currentValue) => {
                        const newValue = currentValue === value ? "" : currentValue
                        onValueChange?.(newValue)
                        if (newValue) {
                          setSelectedCustomer(customer)
                        } else {
                          setSelectedCustomer(null)
                        }
                        setOpen(false)
                        setSearchValue("")
                      }}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center min-w-0 flex-1">
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4 shrink-0",
                            value === customer.id.toString() ? "opacity-100" : "opacity-0"
                          )}
                        />
                        <div className="min-w-0 flex-1">
                          <div className="font-medium truncate">{customer.name}</div>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Mail className="mr-1 h-3 w-3 shrink-0" />
                            <span className="truncate">{customer.email}</span>
                          </div>
                        </div>
                      </div>

                      {/* Show status badge */}
                      {getStatusBadge(customer.status)}
                    </CommandItem>
                  ))}

                  {/* Load more button for pagination */}
                  {hasMore && (
                    <div className="p-2 border-t">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={loadMore}
                        disabled={isSearching}
                        className="w-full"
                      >
                        {isSearching ? (
                          <>
                            <Loader2 className="h-3 w-3 animate-spin mr-2" />
                            Loading more...
                          </>
                        ) : (
                          `Load more customers (${totalResults - customers.length} remaining)`
                        )}
                      </Button>
                    </div>
                  )}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
