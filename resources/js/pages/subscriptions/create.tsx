import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { ArrowLeft, Save, Search } from 'lucide-react';
import { 
  Command, 
  CommandEmpty, 
  CommandGroup, 
  CommandInput, 
  CommandItem, 
  CommandList 
} from '@/components/ui/command';
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from '@/components/ui/popover';

interface Customer {
  id: number;
  name: string;
  email: string;
}

interface CreateSubscriptionProps {
  customers: Customer[];
}

export default function CreateSubscription({ customers }: CreateSubscriptionProps) {
  const [customerSearchOpen, setCustomerSearchOpen] = useState(false);
  const [customerSearch, setCustomerSearch] = useState('');
  
  const { data, setData, post, processing, errors, reset } = useForm({
    customer_id: '',
    name: '',
    description: '',
    price: '',
    billing_cycle: 'monthly',
    start_date: new Date().toISOString().split('T')[0],
    status: 'active',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post(route('subscriptions.store'), {
      onSuccess: () => reset()
    });
  };

  const filteredCustomers = customerSearch === ''
    ? customers
    : customers.filter((customer) =>
        customer.name.toLowerCase().includes(customerSearch.toLowerCase()) ||
        customer.email.toLowerCase().includes(customerSearch.toLowerCase())
      );

  const selectedCustomer = customers.find(c => c.id.toString() === data.customer_id);

  return (
    <AppLayout>
      <Head title="Create Subscription" />
      
      <div className="container mx-auto p-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-2">
              <Link href={route('subscriptions.index')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Subscriptions
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">Create Subscription</h1>
          </div>
        </div>
        
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-6">
              {/* Customer Selection */}
              <Card>
                <CardHeader>
                  <CardTitle>Customer Information</CardTitle>
                  <CardDescription>Select the customer for this subscription</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="customer">Customer <span className="text-red-500">*</span></Label>
                    <Popover open={customerSearchOpen} onOpenChange={setCustomerSearchOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={customerSearchOpen}
                          className="w-full justify-between"
                        >
                          {selectedCustomer ? selectedCustomer.name : "Select customer..."}
                          <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <Command>
                          <CommandInput 
                            placeholder="Search customers..." 
                            value={customerSearch}
                            onValueChange={setCustomerSearch}
                          />
                          <CommandList>
                            <CommandEmpty>No customers found.</CommandEmpty>
                            <CommandGroup>
                              {filteredCustomers.map((customer) => (
                                <CommandItem
                                  key={customer.id}
                                  value={customer.id.toString()}
                                  onSelect={(value) => {
                                    setData('customer_id', value);
                                    setCustomerSearchOpen(false);
                                  }}
                                >
                                  <div className="flex flex-col">
                                    <span>{customer.name}</span>
                                    <span className="text-sm text-gray-500">{customer.email}</span>
                                  </div>
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    {errors.customer_id && <p className="text-red-500 text-sm">{errors.customer_id}</p>}
                  </div>
                  
                  <div className="flex justify-between">
                    <Button 
                      type="button" 
                      variant="outline" 
                      asChild
                    >
                      <Link href={route('customers.create')}>
                        Create New Customer
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
              
              {/* Subscription Details */}
              <Card>
                <CardHeader>
                  <CardTitle>Subscription Details</CardTitle>
                  <CardDescription>Enter the subscription details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name <span className="text-red-500">*</span></Label>
                    <Input 
                      id="name" 
                      value={data.name} 
                      onChange={e => setData('name', e.target.value)}
                      placeholder="Subscription name"
                    />
                    {errors.name && <p className="text-red-500 text-sm">{errors.name}</p>}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea 
                      id="description" 
                      value={data.description} 
                      onChange={e => setData('description', e.target.value)}
                      placeholder="Subscription description"
                      rows={3}
                    />
                    {errors.description && <p className="text-red-500 text-sm">{errors.description}</p>}
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="space-y-6">
              {/* Billing Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Billing Information</CardTitle>
                  <CardDescription>Set up the billing details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">Price <span className="text-red-500">*</span></Label>
                    <div className="relative">
                      <span className="absolute left-3 top-2.5">$</span>
                      <Input 
                        id="price" 
                        type="number" 
                        step="0.01" 
                        min="0" 
                        value={data.price} 
                        onChange={e => setData('price', e.target.value)}
                        placeholder="0.00"
                        className="pl-7"
                      />
                    </div>
                    {errors.price && <p className="text-red-500 text-sm">{errors.price}</p>}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="billing_cycle">Billing Cycle <span className="text-red-500">*</span></Label>
                    <Select 
                      value={data.billing_cycle} 
                      onValueChange={value => setData('billing_cycle', value)}
                    >
                      <SelectTrigger id="billing_cycle">
                        <SelectValue placeholder="Select billing cycle" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                        <SelectItem value="biannually">Biannually</SelectItem>
                        <SelectItem value="annually">Annually</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.billing_cycle && <p className="text-red-500 text-sm">{errors.billing_cycle}</p>}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="start_date">Start Date <span className="text-red-500">*</span></Label>
                    <Input 
                      id="start_date" 
                      type="date" 
                      value={data.start_date} 
                      onChange={e => setData('start_date', e.target.value)}
                    />
                    {errors.start_date && <p className="text-red-500 text-sm">{errors.start_date}</p>}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select 
                      value={data.status} 
                      onValueChange={value => setData('status', value)}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.status && <p className="text-red-500 text-sm">{errors.status}</p>}
                  </div>
                </CardContent>
              </Card>
              
              {/* Submit Button */}
              <Card>
                <CardContent className="pt-6">
                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={processing}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Create Subscription
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
