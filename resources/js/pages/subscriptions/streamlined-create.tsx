import React, { useState, useEffect } from 'react';
import { Head, useForm, Link } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { CustomerCombobox } from '@/components/ui/customer-combobox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  User,
  Zap,
  Calendar,
  DollarSign,
  Download,
  Upload,
  Settings,
  ArrowLeft,
  AlertCircle,
  CheckCircle,
  Wifi,
  FileText
} from 'lucide-react';

interface Customer {
  id: number;
  name: string;
  email: string;
  status: string;
}

interface BandwidthPlan {
  id: number;
  name: string;
  description: string | null;
  download_speed: number;
  upload_speed: number;
  price: number | null;
  priority: number;
  formatted_speed: string;
  formatted_price: string;
}

interface StreamlinedCreateProps {
  customers: Customer[];
  bandwidthPlans: BandwidthPlan[];
  selectedCustomer?: Customer | null;
}

export default function StreamlinedCreateSubscription({
  customers,
  bandwidthPlans,
  selectedCustomer
}: StreamlinedCreateProps) {
  const { data, setData, post, processing, errors, reset } = useForm({
    customer_id: selectedCustomer?.id || '',
    bandwidth_plan_id: '',
    billing_cycle: 'monthly',
    start_date: new Date().toISOString().split('T')[0],
    end_date: '',
    customer_notes: '',
    allow_overrides: false,
    override_name: '',
    override_description: '',
    override_price: '',
  });

  const [selectedPlan, setSelectedPlan] = useState<BandwidthPlan | null>(null);
  const [showOverrides, setShowOverrides] = useState(false);

  // Update selected plan when bandwidth_plan_id changes
  useEffect(() => {
    if (data.bandwidth_plan_id) {
      const plan = bandwidthPlans.find(p => p.id.toString() === data.bandwidth_plan_id);
      setSelectedPlan(plan || null);

      // Auto-populate override fields with plan data
      if (plan && !data.allow_overrides) {
        setData(prev => ({
          ...prev,
          override_name: plan.name,
          override_description: plan.description || '',
          override_price: plan.price?.toString() || '',
        }));
      }
    } else {
      setSelectedPlan(null);
    }
  }, [data.bandwidth_plan_id]);

  // Toggle override fields visibility
  useEffect(() => {
    setShowOverrides(data.allow_overrides);
  }, [data.allow_overrides]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post(route('subscriptions.streamlined.store'));
  };

  const handlePlanSelect = (planId: string) => {
    setData('bandwidth_plan_id', planId);
  };

  const formatPrice = (price: number | null): string => {
    if (!price) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  return (
    <AppLayout>
      <Head title="Create Subscription from Plan" />

      <div className="page-container">
        {/* Header */}
        <div className="page-header">
          <div className="page-title">
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm" asChild>
                <Link href="/subscriptions">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Link>
              </Button>
              <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                <Wifi className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h1>Create Subscription from Plan</h1>
                <p>Streamlined subscription creation using bandwidth plans</p>
              </div>
            </div>
          </div>
        </div>

        {/* Success/Error Messages */}
        {Object.keys(errors).length > 0 && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Please correct the errors below and try again.
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Customer Selection */}
          <Card className="card-modern">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Customer Selection
              </CardTitle>
              <CardDescription>
                Select the customer for this subscription
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="customer_id">
                  Customer <span className="text-destructive">*</span>
                </Label>
                <CustomerCombobox
                  value={data.customer_id.toString()}
                  onValueChange={(value) => setData('customer_id', value)}
                  customers={customers}
                  placeholder="Search and select a customer"
                  className="w-full"
                  showStatus={true}
                  filterActiveOnly={true}
                />
                {errors.customer_id && <p className="text-sm text-destructive">{errors.customer_id}</p>}
              </div>
            </CardContent>
          </Card>

          {/* Bandwidth Plan Selection */}
          <Card className="card-modern">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Bandwidth Plan Selection
              </CardTitle>
              <CardDescription>
                Choose a bandwidth plan - subscription details will be auto-populated
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="bandwidth_plan_id">
                    Bandwidth Plan <span className="text-destructive">*</span>
                  </Label>
                  <Select value={data.bandwidth_plan_id.toString()} onValueChange={handlePlanSelect}>
                    <SelectTrigger className={errors.bandwidth_plan_id ? 'border-destructive' : ''}>
                      <SelectValue placeholder="Select a bandwidth plan" />
                    </SelectTrigger>
                    <SelectContent>
                      {bandwidthPlans.map((plan) => (
                        <SelectItem key={plan.id} value={plan.id.toString()}>
                          <div className="flex flex-col">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{plan.name}</span>
                              <Badge variant="outline">{plan.formatted_speed}</Badge>
                              <Badge variant="secondary">{plan.formatted_price}</Badge>
                            </div>
                            {plan.description && (
                              <span className="text-sm text-muted-foreground">{plan.description}</span>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.bandwidth_plan_id && <p className="text-sm text-destructive">{errors.bandwidth_plan_id}</p>}
                </div>

                {/* Plan Preview */}
                {selectedPlan && (
                  <div className="p-4 bg-muted/50 rounded-lg border">
                    <h4 className="font-medium mb-3 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      Selected Plan Details
                    </h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Download:</span>
                        <div className="flex items-center gap-1 font-medium">
                          <Download className="h-3 w-3" />
                          {selectedPlan.download_speed} Mbps
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Upload:</span>
                        <div className="flex items-center gap-1 font-medium">
                          <Upload className="h-3 w-3" />
                          {selectedPlan.upload_speed} Mbps
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Price:</span>
                        <div className="flex items-center gap-1 font-medium">
                          <DollarSign className="h-3 w-3" />
                          {formatPrice(selectedPlan.price)}
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Priority:</span>
                        <div className="font-medium">{selectedPlan.priority}</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Subscription Settings */}
          <Card className="card-modern">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Subscription Settings
              </CardTitle>
              <CardDescription>
                Configure billing and timing details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="billing_cycle">
                    Billing Cycle <span className="text-destructive">*</span>
                  </Label>
                  <Select value={data.billing_cycle} onValueChange={(value) => setData('billing_cycle', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="quarterly">Quarterly (3 months)</SelectItem>
                      <SelectItem value="biannually">Biannually (6 months)</SelectItem>
                      <SelectItem value="annually">Annually (12 months)</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.billing_cycle && <p className="text-sm text-destructive">{errors.billing_cycle}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="start_date">
                    Start Date <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="start_date"
                    type="date"
                    value={data.start_date}
                    onChange={(e) => setData('start_date', e.target.value)}
                    className={errors.start_date ? 'border-destructive' : ''}
                  />
                  {errors.start_date && <p className="text-sm text-destructive">{errors.start_date}</p>}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="end_date">End Date (Optional)</Label>
                <Input
                  id="end_date"
                  type="date"
                  value={data.end_date}
                  onChange={(e) => setData('end_date', e.target.value)}
                  className={errors.end_date ? 'border-destructive' : ''}
                />
                <p className="text-sm text-muted-foreground">
                  Leave empty for ongoing subscription
                </p>
                {errors.end_date && <p className="text-sm text-destructive">{errors.end_date}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="customer_notes">Customer Notes</Label>
                <Textarea
                  id="customer_notes"
                  value={data.customer_notes}
                  onChange={(e) => setData('customer_notes', e.target.value)}
                  rows={3}
                  placeholder="Any special notes or requirements for this customer..."
                  className={errors.customer_notes ? 'border-destructive' : ''}
                />
                {errors.customer_notes && <p className="text-sm text-destructive">{errors.customer_notes}</p>}
              </div>
            </CardContent>
          </Card>

          {/* Override Options */}
          <Card className="card-modern">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Override Options
              </CardTitle>
              <CardDescription>
                Customize subscription details if needed
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="allow_overrides"
                  checked={data.allow_overrides}
                  onCheckedChange={(checked) => setData('allow_overrides', checked === true)}
                />
                <Label htmlFor="allow_overrides">Allow custom overrides for this subscription</Label>
              </div>
              <p className="text-sm text-muted-foreground">
                Enable this to customize the subscription name, description, or price
              </p>

              {showOverrides && (
                <div className="space-y-4 p-4 bg-muted/50 rounded-lg border">
                  <h4 className="font-medium">Custom Override Fields</h4>

                  <div className="space-y-2">
                    <Label htmlFor="override_name">Custom Subscription Name</Label>
                    <Input
                      id="override_name"
                      value={data.override_name}
                      onChange={(e) => setData('override_name', e.target.value)}
                      placeholder={selectedPlan?.name || 'Enter custom name'}
                      className={errors.override_name ? 'border-destructive' : ''}
                    />
                    {errors.override_name && <p className="text-sm text-destructive">{errors.override_name}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="override_description">Custom Description</Label>
                    <Textarea
                      id="override_description"
                      value={data.override_description}
                      onChange={(e) => setData('override_description', e.target.value)}
                      placeholder={selectedPlan?.description || 'Enter custom description'}
                      rows={2}
                      className={errors.override_description ? 'border-destructive' : ''}
                    />
                    {errors.override_description && <p className="text-sm text-destructive">{errors.override_description}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="override_price">Custom Price (USD)</Label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="override_price"
                        type="number"
                        min="0"
                        step="0.01"
                        value={data.override_price}
                        onChange={(e) => setData('override_price', e.target.value)}
                        placeholder={selectedPlan?.price?.toString() || '0.00'}
                        className={`pl-10 ${errors.override_price ? 'border-destructive' : ''}`}
                      />
                    </div>
                    {errors.override_price && <p className="text-sm text-destructive">{errors.override_price}</p>}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end gap-4">
            <Button variant="outline" asChild>
              <Link href="/subscriptions">Cancel</Link>
            </Button>
            <Button type="submit" disabled={processing}>
              {processing ? 'Creating...' : 'Create Subscription'}
            </Button>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}