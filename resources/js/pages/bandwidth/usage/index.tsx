import { useState, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import { type BreadcrumbItem } from '@/types';
import axios from 'axios';

// Define the BandwidthUsage type
interface BandwidthUsage {
  id: number;
  usageable_type: string;
  usageable_id: number;
  download: number;
  upload: number;
  total: number;
  period_start: string;
  period_end: string;
  created_at: string;
  updated_at: string;
  usageable?: {
    id: number;
    name?: string;
    username?: string;
    [key: string]: unknown;
  };
}

// Define the pagination type
interface Pagination {
  current_page: number;
  data: BandwidthUsage[];
  from: number;
  last_page: number;
  per_page: number;
  to: number;
  total: number;
}

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'Bandwidth',
    href: '/bandwidth',
  },
  {
    title: 'Usage',
    href: '/bandwidth/usage',
  },
];

export default function BandwidthUsageList() {
  const [usageData, setUsageData] = useState<Pagination | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [search, setSearch] = useState<string>('');
  const [usageableType, setUsageableType] = useState<string>('');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [sortField, setSortField] = useState<string>('total');
  const [sortDirection, setSortDirection] = useState<string>('desc');
  const [perPage, setPerPage] = useState<number>(15);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const fetchUsage = async () => {
    setLoading(true);
    try {
      // Get customer_id from URL if present
      const urlParams = new URLSearchParams(window.location.search);
      const customerId = urlParams.get('customer_id');

      const response = await axios.get('/api/bandwidth/usage', {
        params: {
          search,
          customer_id: customerId || undefined,
          usageable_type: usageableType === 'all' ? undefined : usageableType,
          period_start: startDate || undefined,
          period_end: endDate || undefined,
          sort_field: sortField,
          sort_direction: sortDirection,
          per_page: perPage,
          page: currentPage,
        },
      });
      setUsageData(response.data);
    } catch (error) {
      console.error('Error fetching bandwidth usage:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsage();
  }, [search, usageableType, startDate, endDate, sortField, sortDirection, perPage, currentPage]);

  const handleSort = (field: string) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc'); // Default to descending for usage data
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const getUsageableName = (usage: BandwidthUsage): string => {
    if (!usage.usageable) {
      return `${usage.usageable_type} #${usage.usageable_id}`;
    }

    if (usage.usageable.name) {
      return usage.usageable.name;
    }

    if (usage.usageable.username) {
      return usage.usageable.username;
    }

    return `${usage.usageable_type} #${usage.usageable_id}`;
  };

  const formatUsageableType = (type: string): string => {
    // Convert "App\Models\User" to "User"
    const parts = type.split('\\');
    return parts[parts.length - 1];
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Bandwidth Usage" />
      <div className="flex flex-col gap-4 p-4">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">Bandwidth Usage</h1>
          <div className="flex gap-2">
            <button
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
              onClick={() => window.location.href = '/bandwidth/usage/export'}
            >
              Export Data
            </button>
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
              onClick={() => window.location.href = '/bandwidth/usage/dashboard'}
            >
              Usage Dashboard
            </button>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-4 mb-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search..."
              className="w-full px-4 py-2 border rounded-md"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
          <div>
            <select
              className="w-full px-4 py-2 border rounded-md"
              value={usageableType}
              onChange={(e) => setUsageableType(e.target.value)}
            >
              <option value="all">All Types</option>
              <option value="App\Models\User">User</option>
              <option value="App\Models\Device">Device</option>
              <option value="App\Models\Network">Network</option>
            </select>
          </div>
          <div>
            <input
              type="date"
              placeholder="Start Date"
              className="w-full px-4 py-2 border rounded-md"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
            />
          </div>
          <div>
            <input
              type="date"
              placeholder="End Date"
              className="w-full px-4 py-2 border rounded-md"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
            />
          </div>
          <div>
            <select
              className="w-full px-4 py-2 border rounded-md"
              value={perPage.toString()}
              onChange={(e) => setPerPage(parseInt(e.target.value))}
            >
              <option value="10">10 per page</option>
              <option value="15">15 per page</option>
              <option value="25">25 per page</option>
              <option value="50">50 per page</option>
            </select>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white border rounded-md">
                <thead className="bg-gray-100">
                  <tr>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('usageable_type')}
                    >
                      Type
                      {sortField === 'usageable_type' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th className="px-4 py-2 text-left">Entity</th>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('download')}
                    >
                      Download
                      {sortField === 'download' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('upload')}
                    >
                      Upload
                      {sortField === 'upload' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('total')}
                    >
                      Total
                      {sortField === 'total' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('period_start')}
                    >
                      Period
                      {sortField === 'period_start' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th className="px-4 py-2 text-left">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {usageData && usageData.data.length > 0 ? (
                    usageData.data.map((usage) => (
                      <tr key={usage.id} className="border-t hover:bg-gray-50">
                        <td className="px-4 py-2">{formatUsageableType(usage.usageable_type)}</td>
                        <td className="px-4 py-2">
                          <a
                            href={`/${formatUsageableType(usage.usageable_type).toLowerCase()}s/${usage.usageable_id}`}
                            className="text-blue-500 hover:underline"
                          >
                            {getUsageableName(usage)}
                          </a>
                        </td>
                        <td className="px-4 py-2">{formatBytes(usage.download)}</td>
                        <td className="px-4 py-2">{formatBytes(usage.upload)}</td>
                        <td className="px-4 py-2 font-semibold">{formatBytes(usage.total)}</td>
                        <td className="px-4 py-2">
                          {formatDate(usage.period_start)} - {formatDate(usage.period_end)}
                        </td>
                        <td className="px-4 py-2">
                          <div className="flex space-x-2">
                            <button
                              className="px-2 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                              onClick={() => window.location.href = `/bandwidth/usage/${usage.id}`}
                            >
                              Details
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={7} className="px-4 py-8 text-center text-gray-500">
                        No bandwidth usage data found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {usageData && (
              <div className="flex justify-between items-center mt-4">
                <div>
                  Showing {usageData.from} to {usageData.to} of {usageData.total} records
                </div>
                <div className="flex space-x-2">
                  {Array.from({ length: usageData.last_page }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      className={`px-3 py-1 rounded-md ${
                        page === usageData.current_page
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 hover:bg-gray-300'
                      }`}
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </AppLayout>
  );
}
