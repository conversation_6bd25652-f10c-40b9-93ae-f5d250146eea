import React, { useState, useEffect } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { ArrowLeft, Save, Plus, Trash, Search } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';

interface Customer {
  id: number;
  name: string;
  email: string;
}

interface Subscription {
  id: number;
  name: string;
  price: string;
  billing_cycle: string;
}

interface InvoiceItem {
  description: string;
  quantity: number;
  unit_price: number;
}

interface CreateInvoiceProps {
  customers: Customer[];
}

export default function CreateInvoice({ customers }: CreateInvoiceProps) {
  const [customerSearchOpen, setCustomerSearchOpen] = useState(false);
  const [customerSearch, setCustomerSearch] = useState('');
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [items, setItems] = useState<InvoiceItem[]>([
    { description: '', quantity: 1, unit_price: 0 }
  ]);
  const [totalAmount, setTotalAmount] = useState<number>(0);

  const { data, setData, post, processing, errors, reset } = useForm({
    customer_id: '',
    subscription_id: '',
    invoice_number: `INV-${Date.now()}`,
    issue_date: new Date().toISOString().split('T')[0],
    due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    total_amount: 0,
    status: 'pending',
    notes: '',
    items: items,
  });

  // Calculate total amount whenever items change
  useEffect(() => {
    const total = items.reduce((sum, item) => {
      return sum + (item.quantity * item.unit_price);
    }, 0);

    setTotalAmount(total);
    setData('total_amount', total);
    setData('items', items);
  }, [items]);

  // Fetch customer subscriptions when customer changes
  useEffect(() => {
    if (data.customer_id) {
      // In a real app, this would be an API call
      // For now, we'll just simulate it
      const fetchSubscriptions = async () => {
        try {
          const response = await fetch(`/api/customers/${data.customer_id}/subscriptions`);
          if (response.ok) {
            const data = await response.json();
            setSubscriptions(data.data || []);
          }
        } catch (error) {
          console.error('Error fetching subscriptions:', error);
          setSubscriptions([]);
        }
      };

      fetchSubscriptions();
    } else {
      setSubscriptions([]);
    }
  }, [data.customer_id]);

  const handleItemChange = (index: number, field: keyof InvoiceItem, value: string | number) => {
    const newItems = [...items];

    if (field === 'quantity' || field === 'unit_price') {
      newItems[index][field] = parseFloat(value as string) || 0;
    } else {
      newItems[index][field] = value;
    }

    setItems(newItems);
  };

  const addItem = () => {
    setItems([...items, { description: '', quantity: 1, unit_price: 0 }]);
  };

  const removeItem = (index: number) => {
    if (items.length > 1) {
      const newItems = [...items];
      newItems.splice(index, 1);
      setItems(newItems);
    }
  };

  const handleCustomerSelect = (customerId: string) => {
    setData('customer_id', customerId);
    setData('subscription_id', '');
    setCustomerSearchOpen(false);
  };

  const handleSubscriptionSelect = (subscriptionId: string) => {
    // If "none" is selected, set subscription_id to empty string in the form data
    setData('subscription_id', subscriptionId === 'none' ? '' : subscriptionId);

    if (subscriptionId && subscriptionId !== 'none') {
      const subscription = subscriptions.find(s => s.id.toString() === subscriptionId);
      if (subscription) {
        // Add subscription as an item
        setItems([
          {
            description: `${subscription.name} (${subscription.billing_cycle})`,
            quantity: 1,
            unit_price: parseFloat(subscription.price)
          }
        ]);
      }
    } else {
      // Reset to default empty item if "none" is selected
      setItems([{ description: '', quantity: 1, unit_price: 0 }]);
    }
  };

  const filteredCustomers = customerSearch === ''
    ? customers
    : customers.filter((customer) =>
        customer.name.toLowerCase().includes(customerSearch.toLowerCase()) ||
        customer.email.toLowerCase().includes(customerSearch.toLowerCase())
      );

  const selectedCustomer = customers.find(c => c.id.toString() === data.customer_id);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post(route('invoices.store'));
  };

  return (
    <AppLayout>
      <Head title="Create Invoice" />

      <div className="page-container">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-2">
              <Link href={route('invoices.index')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Invoices
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">Create Invoice</h1>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-6">
              {/* Customer Selection */}
              <Card>
                <CardHeader>
                  <CardTitle>Customer Information</CardTitle>
                  <CardDescription>Select the customer for this invoice</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="customer">Customer <span className="text-red-500">*</span></Label>
                    <Popover open={customerSearchOpen} onOpenChange={setCustomerSearchOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={customerSearchOpen}
                          className="w-full justify-between"
                        >
                          {selectedCustomer ? selectedCustomer.name : "Select customer..."}
                          <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <Command>
                          <CommandInput
                            placeholder="Search customers..."
                            value={customerSearch}
                            onValueChange={setCustomerSearch}
                          />
                          <CommandList>
                            <CommandEmpty>No customers found.</CommandEmpty>
                            <CommandGroup>
                              {filteredCustomers.map((customer) => (
                                <CommandItem
                                  key={customer.id}
                                  value={customer.id.toString()}
                                  onSelect={(value) => handleCustomerSelect(value)}
                                >
                                  <div className="flex flex-col">
                                    <span>{customer.name}</span>
                                    <span className="text-sm text-gray-500">{customer.email}</span>
                                  </div>
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    {errors.customer_id && <p className="text-red-500 text-sm">{errors.customer_id}</p>}
                  </div>

                  {subscriptions.length > 0 && (
                    <div className="space-y-2">
                      <Label htmlFor="subscription_id">Subscription (Optional)</Label>
                      <Select
                        value={data.subscription_id}
                        onValueChange={handleSubscriptionSelect}
                      >
                        <SelectTrigger id="subscription_id">
                          <SelectValue placeholder="Select a subscription" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">No subscription</SelectItem>
                          {subscriptions.map((subscription) => (
                            <SelectItem key={subscription.id} value={subscription.id.toString()}>
                              {subscription.name} - ${subscription.price}/{subscription.billing_cycle}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.subscription_id && <p className="text-red-500 text-sm">{errors.subscription_id}</p>}
                    </div>
                  )}

                  <div className="flex justify-between">
                    <Button
                      type="button"
                      variant="outline"
                      asChild
                    >
                      <Link href={route('customers.create')}>
                        Create New Customer
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Invoice Details */}
              <Card>
                <CardHeader>
                  <CardTitle>Invoice Details</CardTitle>
                  <CardDescription>Enter the invoice details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="invoice_number">Invoice Number <span className="text-red-500">*</span></Label>
                    <Input
                      id="invoice_number"
                      value={data.invoice_number}
                      onChange={e => setData('invoice_number', e.target.value)}
                      placeholder="INV-12345"
                    />
                    {errors.invoice_number && <p className="text-red-500 text-sm">{errors.invoice_number}</p>}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="issue_date">Issue Date <span className="text-red-500">*</span></Label>
                      <Input
                        id="issue_date"
                        type="date"
                        value={data.issue_date}
                        onChange={e => setData('issue_date', e.target.value)}
                      />
                      {errors.issue_date && <p className="text-red-500 text-sm">{errors.issue_date}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="due_date">Due Date <span className="text-red-500">*</span></Label>
                      <Input
                        id="due_date"
                        type="date"
                        value={data.due_date}
                        onChange={e => setData('due_date', e.target.value)}
                      />
                      {errors.due_date && <p className="text-red-500 text-sm">{errors.due_date}</p>}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={data.status}
                      onValueChange={value => setData('status', value)}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="paid">Paid</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.status && <p className="text-red-500 text-sm">{errors.status}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes">Notes</Label>
                    <Textarea
                      id="notes"
                      value={data.notes}
                      onChange={e => setData('notes', e.target.value)}
                      placeholder="Additional notes for the invoice"
                      rows={3}
                    />
                    {errors.notes && <p className="text-red-500 text-sm">{errors.notes}</p>}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              {/* Invoice Items */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>Invoice Items</CardTitle>
                    <CardDescription>Add items to the invoice</CardDescription>
                  </div>
                  <Button type="button" onClick={addItem} size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Item
                  </Button>
                </CardHeader>
                <CardContent className="space-y-4">
                  {items.map((item, index) => (
                    <div key={index} className="space-y-4 p-4 border rounded-md">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium">Item {index + 1}</h4>
                        {items.length > 1 && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeItem(index)}
                          >
                            <Trash className="h-4 w-4 text-red-500" />
                          </Button>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`item-${index}-description`}>Description <span className="text-red-500">*</span></Label>
                        <Input
                          id={`item-${index}-description`}
                          value={item.description}
                          onChange={e => handleItemChange(index, 'description', e.target.value)}
                          placeholder="Item description"
                        />
                        {errors[`items.${index}.description`] && (
                          <p className="text-red-500 text-sm">{errors[`items.${index}.description`]}</p>
                        )}
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`item-${index}-quantity`}>Quantity <span className="text-red-500">*</span></Label>
                          <Input
                            id={`item-${index}-quantity`}
                            type="number"
                            min="1"
                            step="1"
                            value={item.quantity}
                            onChange={e => handleItemChange(index, 'quantity', e.target.value)}
                          />
                          {errors[`items.${index}.quantity`] && (
                            <p className="text-red-500 text-sm">{errors[`items.${index}.quantity`]}</p>
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor={`item-${index}-unit_price`}>Unit Price <span className="text-red-500">*</span></Label>
                          <div className="relative">
                            <span className="absolute left-3 top-2.5">$</span>
                            <Input
                              id={`item-${index}-unit_price`}
                              type="number"
                              step="0.01"
                              min="0"
                              value={item.unit_price}
                              onChange={e => handleItemChange(index, 'unit_price', e.target.value)}
                              className="pl-7"
                            />
                          </div>
                          {errors[`items.${index}.unit_price`] && (
                            <p className="text-red-500 text-sm">{errors[`items.${index}.unit_price`]}</p>
                          )}
                        </div>
                      </div>

                      <div className="text-right font-medium">
                        Subtotal: {formatCurrency(item.quantity * item.unit_price)}
                      </div>
                    </div>
                  ))}

                  {errors.items && <p className="text-red-500 text-sm">{errors.items}</p>}

                  <div className="flex justify-between items-center pt-4 border-t">
                    <span className="font-bold">Total Amount:</span>
                    <span className="font-bold text-xl">{formatCurrency(totalAmount)}</span>
                  </div>
                </CardContent>
              </Card>

              {/* Submit Button */}
              <Card>
                <CardContent className="pt-6">
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={processing}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Create Invoice
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
