import { StatsCard } from '@/components/ui/stats-card';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ModernTable, ModernTableBody, ModernTableCell, ModernTableHeader, ModernTableRow } from '@/components/ui/modern-table';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import {
    Users,
    Wifi,
    Activity,
    DollarSign,
    TrendingUp,
    TrendingDown,
    Server,
    Globe,
    AlertTriangle,
    CheckCircle
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

interface DashboardStats {
    totalCustomers: number;
    activeServices: number;
    totalRevenue: number;
    networkUptime: number;
    customerGrowth: string;
    revenueGrowth: string;
    serviceGrowth: string;
    uptimeChange: string;
    breakdown: {
        static_ip_services: number;
        pppoe_services: number;
        active_customers: number;
        total_devices: number;
        active_devices: number;
    };
}

interface DashboardActivity {
    id: string;
    customer: string;
    action: string;
    service: string;
    time: string;
    status: string;
}

interface NetworkDevice {
    id: number;
    name: string;
    location: string;
    status: string;
    uptime: string;
}

interface DashboardProps {
    stats: DashboardStats;
    recentActivities: DashboardActivity[];
    networkDevices: NetworkDevice[];
}

export default function Dashboard({ stats, recentActivities, networkDevices }: DashboardProps) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="page-container">
                {/* Welcome Section */}
                <div className="page-title">
                    <h1>Welcome back!</h1>
                    <p>Here's what's happening with your ISP network today.</p>
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <StatsCard
                        title="Total Customers"
                        value={stats.totalCustomers.toLocaleString()}
                        change={{
                            value: stats.customerGrowth,
                            type: stats.customerGrowth.startsWith('+') ? 'positive' : 'negative'
                        }}
                        icon={Users}
                        gradient
                    />
                    <StatsCard
                        title="Active Services"
                        value={stats.activeServices.toLocaleString()}
                        change={{
                            value: stats.serviceGrowth,
                            type: stats.serviceGrowth.startsWith('+') ? 'positive' : 'negative'
                        }}
                        icon={Wifi}
                    />
                    <StatsCard
                        title="Monthly Revenue"
                        value={`$${stats.totalRevenue.toLocaleString()}`}
                        change={{
                            value: stats.revenueGrowth,
                            type: stats.revenueGrowth.startsWith('+') ? 'positive' : 'negative'
                        }}
                        icon={DollarSign}
                    />
                    <StatsCard
                        title="Network Uptime"
                        value={`${stats.networkUptime}%`}
                        change={{
                            value: stats.uptimeChange,
                            type: stats.uptimeChange.startsWith('+') ? 'positive' : 'negative'
                        }}
                        icon={Activity}
                    />
                </div>

                {/* Main Content Grid */}
                <div className="grid gap-6 lg:grid-cols-3">
                    {/* Recent Activities */}
                    <div className="lg:col-span-2">
                        <Card className="card-modern">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Activity className="h-5 w-5" />
                                    Recent Activities
                                </CardTitle>
                                <CardDescription>
                                    Latest customer and service activities
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="p-0">
                                {recentActivities.length > 0 ? (
                                    <ModernTable>
                                        <ModernTableHeader>
                                            <ModernTableRow>
                                                <ModernTableCell header>Customer</ModernTableCell>
                                                <ModernTableCell header>Action</ModernTableCell>
                                                <ModernTableCell header>Service</ModernTableCell>
                                                <ModernTableCell header>Time</ModernTableCell>
                                                <ModernTableCell header>Status</ModernTableCell>
                                            </ModernTableRow>
                                        </ModernTableHeader>
                                        <ModernTableBody>
                                            {recentActivities.map((activity) => (
                                                <ModernTableRow key={activity.id}>
                                                    <ModernTableCell className="font-medium">
                                                        {activity.customer}
                                                    </ModernTableCell>
                                                    <ModernTableCell>{activity.action}</ModernTableCell>
                                                    <ModernTableCell>
                                                        <Badge variant="outline">{activity.service}</Badge>
                                                    </ModernTableCell>
                                                    <ModernTableCell className="text-muted-foreground">
                                                        {activity.time}
                                                    </ModernTableCell>
                                                    <ModernTableCell>
                                                        <Badge
                                                            className={
                                                                activity.status === 'success'
                                                                    ? 'status-badge-active'
                                                                    : activity.status === 'warning'
                                                                    ? 'status-badge-suspended'
                                                                    : activity.status === 'info'
                                                                    ? 'status-badge-inactive'
                                                                    : 'status-badge-inactive'
                                                            }
                                                        >
                                                            {activity.status === 'success' && <CheckCircle className="h-3 w-3 mr-1" />}
                                                            {activity.status === 'warning' && <AlertTriangle className="h-3 w-3 mr-1" />}
                                                            {activity.status.charAt(0).toUpperCase() + activity.status.slice(1)}
                                                        </Badge>
                                                    </ModernTableCell>
                                                </ModernTableRow>
                                            ))}
                                        </ModernTableBody>
                                    </ModernTable>
                                ) : (
                                    <div className="text-center py-8">
                                        <Activity className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                        <h3 className="text-lg font-medium mb-2">No Recent Activities</h3>
                                        <p className="text-muted-foreground">
                                            Recent customer and service activities will appear here.
                                        </p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Network Status */}
                    <div className="space-y-6">
                        <Card className="card-modern">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Server className="h-5 w-5" />
                                    Network Devices
                                </CardTitle>
                                <CardDescription>
                                    Current status of network infrastructure
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {networkDevices.length > 0 ? (
                                    networkDevices.map((device) => (
                                        <div key={device.id} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                                            <div className="space-y-1">
                                                <p className="font-medium text-sm">{device.name}</p>
                                                <p className="text-xs text-muted-foreground">{device.location}</p>
                                            </div>
                                            <div className="text-right space-y-1">
                                                <Badge
                                                    className={
                                                        device.status === 'online'
                                                            ? 'status-badge-active'
                                                            : 'status-badge-suspended'
                                                    }
                                                >
                                                    {device.status === 'online' ? (
                                                        <CheckCircle className="h-3 w-3 mr-1" />
                                                    ) : (
                                                        <AlertTriangle className="h-3 w-3 mr-1" />
                                                    )}
                                                    {device.status}
                                                </Badge>
                                                <p className="text-xs text-muted-foreground">{device.uptime}</p>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <div className="text-center py-6">
                                        <Server className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                        <h3 className="text-lg font-medium mb-2">No Network Devices</h3>
                                        <p className="text-muted-foreground mb-4">
                                            Add network devices to monitor their status.
                                        </p>
                                        <Link
                                            href="/network/devices/create"
                                            className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                                        >
                                            <Server className="h-4 w-4" />
                                            Add Device
                                        </Link>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Quick Actions */}
                        <Card className="card-modern">
                            <CardHeader>
                                <CardTitle>Quick Actions</CardTitle>
                                <CardDescription>
                                    Common management tasks
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <Link
                                    href="/customers/create"
                                    className="flex items-center gap-3 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors"
                                >
                                    <Users className="h-4 w-4 text-primary" />
                                    <span className="text-sm font-medium">Add New Customer</span>
                                </Link>
                                <Link
                                    href="/services"
                                    className="flex items-center gap-3 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors"
                                >
                                    <Wifi className="h-4 w-4 text-primary" />
                                    <span className="text-sm font-medium">Manage Services</span>
                                </Link>
                                <Link
                                    href="/network/devices"
                                    className="flex items-center gap-3 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors"
                                >
                                    <Server className="h-4 w-4 text-primary" />
                                    <span className="text-sm font-medium">Network Devices</span>
                                </Link>
                                <Link
                                    href="/bandwidth/usage"
                                    className="flex items-center gap-3 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors"
                                >
                                    <Activity className="h-4 w-4 text-primary" />
                                    <span className="text-sm font-medium">Bandwidth Usage</span>
                                </Link>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}