import React, { useState, useEffect } from 'react';
import { Head } from '@inertiajs/react';
import axios from 'axios';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Activity,
  CheckCircle2,
  XCircle,
  Clock,
  AlertTriangle,
  Users,
  Server,
  Zap
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

// Declare Echo as global
declare global {
  interface Window {
    Echo: any;
  }
}

export default function ProvisioningDashboard() {
  const [overview, setOverview] = useState({
    currently_provisioning: 0,
    pending_provisioning: 0,
    failed_provisioning: 0,
    recent_completions: 0,
  });
  
  const [recentActivity, setRecentActivity] = useState([]);
  const [isConnectedToWebSocket, setIsConnectedToWebSocket] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);

  // Fetch initial data
  useEffect(() => {
    fetchProvisioningOverview();
  }, []);

  // WebSocket connection for real-time updates
  useEffect(() => {
    if (window.Echo) {
      const channel = window.Echo.join('static-ip-dashboard');
      
      channel.listen('.provisioning.updated', (data) => {
        console.log('Dashboard provisioning update received:', data);
        setLastUpdate(new Date().toLocaleTimeString());
        // Refresh overview data when updates come in
        fetchProvisioningOverview();
      });

      setIsConnectedToWebSocket(true);

      return () => {
        channel.stopListening('.provisioning.updated');
        window.Echo.leaveChannel('static-ip-dashboard');
        setIsConnectedToWebSocket(false);
      };
    }
  }, []);

  const fetchProvisioningOverview = async () => {
    try {
      const response = await axios.get('/api/services/static-ip/provisioning-overview');
      if (response.data.success) {
        setOverview(response.data.overview);
        setRecentActivity(response.data.recent_activity);
      }
    } catch (error) {
      console.error('Failed to fetch provisioning overview:', error);
    }
  };

  const getProvisioningStatusBadge = (status) => {
    switch (status) {
      case 'pending':
        return <Badge className="bg-yellow-500">Pending</Badge>;
      case 'provisioning':
        return <Badge className="bg-blue-500">Provisioning</Badge>;
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'provisioning_failed':
        return <Badge className="bg-red-500">Failed</Badge>;
      case 'reactivation_failed':
        return <Badge className="bg-red-500">Reactivation Failed</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  const getProvisioningIcon = (status) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'provisioning':
        return <Activity className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'active':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'provisioning_failed':
      case 'reactivation_failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <AppLayout>
      <Head title="Provisioning Dashboard" />

      <div className="page-container">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold">Provisioning Dashboard</h1>
            <p className="text-gray-600">Real-time monitoring of Static IP service provisioning</p>
          </div>
          <div className="flex items-center space-x-2">
            {isConnectedToWebSocket && (
              <div className="flex items-center text-green-600 text-sm">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-1"></div>
                Live Updates
              </div>
            )}
            {lastUpdate && (
              <span className="text-xs text-gray-500">Last update: {lastUpdate}</span>
            )}
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Currently Provisioning</CardTitle>
              <Activity className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{overview.currently_provisioning}</div>
              <p className="text-xs text-muted-foreground">Services being provisioned</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Provisioning</CardTitle>
              <Clock className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{overview.pending_provisioning}</div>
              <p className="text-xs text-muted-foreground">Waiting to be provisioned</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Failed Provisioning</CardTitle>
              <XCircle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{overview.failed_provisioning}</div>
              <p className="text-xs text-muted-foreground">Require attention</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Recent Completions</CardTitle>
              <CheckCircle2 className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{overview.recent_completions}</div>
              <p className="text-xs text-muted-foreground">Completed in last hour</p>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Provisioning Activity</CardTitle>
            <CardDescription>Latest provisioning activities across all Static IP services</CardDescription>
          </CardHeader>
          <CardContent>
            {recentActivity.length > 0 ? (
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      {getProvisioningIcon(activity.provisioning_status)}
                      <div>
                        <div className="font-medium">{activity.customer_name}</div>
                        <div className="text-sm text-gray-500">IP: {activity.ip_address}</div>
                        <div className="text-xs text-gray-400">
                          {activity.provisioning_stage || 'No stage information'}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      {getProvisioningStatusBadge(activity.provisioning_status)}
                      <div className="text-sm text-gray-500 mt-1">
                        {activity.provisioning_progress}% complete
                      </div>
                      <div className="text-xs text-gray-400">
                        {activity.provisioning_started_at 
                          ? new Date(activity.provisioning_started_at).toLocaleString()
                          : 'Not started'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Server className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p>No recent provisioning activity</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
