import { AppLayout } from '@/components/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Save, Shield } from 'lucide-react';

interface Permission {
    id: number;
    name: string;
}

interface PermissionGroup {
    [key: string]: Permission[];
}

interface Props {
    permissions: PermissionGroup;
}

export default function CreateRole({ permissions }: Props) {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        permissions: [] as number[],
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.settings.roles.store'));
    };

    const handlePermissionChange = (permissionId: number, checked: boolean) => {
        if (checked) {
            setData('permissions', [...data.permissions, permissionId]);
        } else {
            setData('permissions', data.permissions.filter(id => id !== permissionId));
        }
    };

    const handleGroupToggle = (groupPermissions: Permission[], checked: boolean) => {
        const groupIds = groupPermissions.map(p => p.id);
        if (checked) {
            const newPermissions = [...new Set([...data.permissions, ...groupIds])];
            setData('permissions', newPermissions);
        } else {
            setData('permissions', data.permissions.filter(id => !groupIds.includes(id)));
        }
    };

    const isGroupChecked = (groupPermissions: Permission[]) => {
        return groupPermissions.every(p => data.permissions.includes(p.id));
    };

    const isGroupIndeterminate = (groupPermissions: Permission[]) => {
        const checkedCount = groupPermissions.filter(p => data.permissions.includes(p.id)).length;
        return checkedCount > 0 && checkedCount < groupPermissions.length;
    };

    return (
        <AppLayout>
            <Head title="Create Role" />

            <div className="page-container">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <div className="flex items-center space-x-2">
                            <Link href={route('admin.settings.roles.index')}>
                                <Button variant="ghost" size="sm">
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Back to Roles
                                </Button>
                            </Link>
                        </div>
                        <h1 className="text-3xl font-bold text-gray-900 mt-2">Create Role</h1>
                        <p className="text-gray-600 mt-2">
                            Create a new role and assign permissions
                        </p>
                    </div>
                </div>

                {/* Create Role Form */}
                <Card className="mt-6 max-w-4xl">
                    <CardHeader>
                        <CardTitle className="flex items-center">
                            <Shield className="h-5 w-5 mr-2" />
                            Role Information
                        </CardTitle>
                        <CardDescription>
                            Enter the role name and select permissions
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Role Name */}
                            <div className="space-y-2">
                                <Label htmlFor="name">Role Name</Label>
                                <Input
                                    id="name"
                                    type="text"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    placeholder="Enter role name"
                                    className={errors.name ? 'border-red-500' : ''}
                                />
                                {errors.name && (
                                    <p className="text-sm text-red-600">{errors.name}</p>
                                )}
                            </div>

                            {/* Permissions */}
                            <div className="space-y-4">
                                <Label>Permissions</Label>
                                <div className="space-y-6">
                                    {Object.entries(permissions).map(([groupName, groupPermissions]) => (
                                        <div key={groupName} className="border rounded-lg p-4">
                                            <div className="flex items-center space-x-2 mb-3">
                                                <Checkbox
                                                    id={`group-${groupName}`}
                                                    checked={isGroupChecked(groupPermissions)}
                                                    onCheckedChange={(checked) => 
                                                        handleGroupToggle(groupPermissions, checked as boolean)
                                                    }
                                                    className={isGroupIndeterminate(groupPermissions) ? 'data-[state=checked]:bg-blue-600' : ''}
                                                />
                                                <Label 
                                                    htmlFor={`group-${groupName}`}
                                                    className="text-sm font-semibold capitalize cursor-pointer"
                                                >
                                                    {groupName} ({groupPermissions.length})
                                                </Label>
                                            </div>
                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 ml-6">
                                                {groupPermissions.map((permission) => (
                                                    <div key={permission.id} className="flex items-center space-x-2">
                                                        <Checkbox
                                                            id={`permission-${permission.id}`}
                                                            checked={data.permissions.includes(permission.id)}
                                                            onCheckedChange={(checked) => 
                                                                handlePermissionChange(permission.id, checked as boolean)
                                                            }
                                                        />
                                                        <Label 
                                                            htmlFor={`permission-${permission.id}`}
                                                            className="text-sm font-normal cursor-pointer"
                                                        >
                                                            {permission.name}
                                                        </Label>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                                {errors.permissions && (
                                    <p className="text-sm text-red-600">{errors.permissions}</p>
                                )}
                            </div>

                            {/* Submit Button */}
                            <div className="flex items-center justify-end space-x-4 pt-4">
                                <Link href={route('admin.settings.roles.index')}>
                                    <Button type="button" variant="outline">
                                        Cancel
                                    </Button>
                                </Link>
                                <Button type="submit" disabled={processing}>
                                    <Save className="h-4 w-4 mr-2" />
                                    {processing ? 'Creating...' : 'Create Role'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
