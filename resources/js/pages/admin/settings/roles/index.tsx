import { AppLayout } from '@/components/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Head, Link, router } from '@inertiajs/react';
import { 
    Shield, 
    Plus, 
    Edit, 
    Trash2, 
    Eye,
    Users
} from 'lucide-react';

interface Permission {
    id: number;
    name: string;
}

interface Role {
    id: number;
    name: string;
    permissions: Permission[];
    users_count: number;
    created_at: string;
}

interface PaginatedRoles {
    data: Role[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

interface Props {
    roles: PaginatedRoles;
}

export default function RolesIndex({ roles }: Props) {
    const handleDelete = (role: Role) => {
        if (role.users_count > 0) {
            alert('Cannot delete role that has users assigned to it.');
            return;
        }
        
        if (confirm(`Are you sure you want to delete the role "${role.name}"?`)) {
            router.delete(route('admin.settings.roles.destroy', role.id));
        }
    };

    return (
        <AppLayout>
            <Head title="Role Management" />

            <div className="page-container">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Role Management</h1>
                        <p className="text-gray-600 mt-2">
                            Manage user roles and permissions
                        </p>
                    </div>
                    <div className="flex items-center space-x-4">
                        <Link href={route('admin.settings.roles.create')}>
                            <Button>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Role
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Roles List */}
                <Card className="mt-6">
                    <CardHeader>
                        <CardTitle className="flex items-center">
                            <Shield className="h-5 w-5 mr-2" />
                            Roles ({roles.total})
                        </CardTitle>
                        <CardDescription>
                            Manage system roles and their permissions
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left py-3 px-4 font-medium">Role Name</th>
                                        <th className="text-left py-3 px-4 font-medium">Permissions</th>
                                        <th className="text-left py-3 px-4 font-medium">Users</th>
                                        <th className="text-left py-3 px-4 font-medium">Created</th>
                                        <th className="text-right py-3 px-4 font-medium">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {roles.data.map((role) => (
                                        <tr key={role.id} className="border-b hover:bg-gray-50">
                                            <td className="py-3 px-4">
                                                <div className="font-medium">{role.name}</div>
                                            </td>
                                            <td className="py-3 px-4">
                                                <Badge variant="secondary">
                                                    {role.permissions.length} permissions
                                                </Badge>
                                            </td>
                                            <td className="py-3 px-4">
                                                <div className="flex items-center space-x-1">
                                                    <Users className="h-4 w-4 text-gray-500" />
                                                    <span className="text-gray-600">{role.users_count}</span>
                                                </div>
                                            </td>
                                            <td className="py-3 px-4 text-gray-600">
                                                {new Date(role.created_at).toLocaleDateString()}
                                            </td>
                                            <td className="py-3 px-4">
                                                <div className="flex items-center justify-end space-x-2">
                                                    <Link href={route('admin.settings.roles.show', role.id)}>
                                                        <Button variant="ghost" size="sm">
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    <Link href={route('admin.settings.roles.edit', role.id)}>
                                                        <Button variant="ghost" size="sm">
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    <Button 
                                                        variant="ghost" 
                                                        size="sm"
                                                        onClick={() => handleDelete(role)}
                                                        className="text-red-600 hover:text-red-700"
                                                        disabled={role.users_count > 0}
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        {/* Pagination */}
                        {roles.last_page > 1 && (
                            <div className="flex items-center justify-between mt-6">
                                <div className="text-sm text-gray-600">
                                    Showing {((roles.current_page - 1) * roles.per_page) + 1} to{' '}
                                    {Math.min(roles.current_page * roles.per_page, roles.total)} of{' '}
                                    {roles.total} results
                                </div>
                                <div className="flex items-center space-x-2">
                                    {roles.links.map((link, index) => (
                                        <Button
                                            key={index}
                                            variant={link.active ? "default" : "outline"}
                                            size="sm"
                                            disabled={!link.url}
                                            onClick={() => link.url && router.get(link.url)}
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
