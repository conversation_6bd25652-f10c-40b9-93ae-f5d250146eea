import { AppLayout } from '@/components/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Head, Link } from '@inertiajs/react';
import { 
    ArrowLeft, 
    Edit, 
    User, 
    Mail, 
    Calendar, 
    Shield,
    CheckCircle,
    XCircle
} from 'lucide-react';

interface Permission {
    id: number;
    name: string;
}

interface Role {
    id: number;
    name: string;
    permissions: Permission[];
}

interface User {
    id: number;
    name: string;
    email: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    roles: Role[];
}

interface Props {
    user: User;
}

export default function ShowUser({ user }: Props) {
    const allPermissions = user.roles.flatMap(role => role.permissions);
    const uniquePermissions = allPermissions.filter((permission, index, self) => 
        index === self.findIndex(p => p.id === permission.id)
    );

    return (
        <AppLayout>
            <Head title={`User - ${user.name}`} />

            <div className="page-container">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <div className="flex items-center space-x-2">
                            <Link href={route('admin.settings.users.index')}>
                                <Button variant="ghost" size="sm">
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Back to Users
                                </Button>
                            </Link>
                        </div>
                        <h1 className="text-3xl font-bold text-gray-900 mt-2">{user.name}</h1>
                        <p className="text-gray-600 mt-2">
                            User details and permissions
                        </p>
                    </div>
                    <div className="flex items-center space-x-4">
                        <Link href={route('admin.settings.users.edit', user.id)}>
                            <Button>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit User
                            </Button>
                        </Link>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                    {/* User Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <User className="h-5 w-5 mr-2" />
                                User Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center space-x-3">
                                <Mail className="h-4 w-4 text-gray-500" />
                                <div>
                                    <p className="text-sm text-gray-500">Email</p>
                                    <p className="font-medium">{user.email}</p>
                                </div>
                            </div>

                            <div className="flex items-center space-x-3">
                                <Calendar className="h-4 w-4 text-gray-500" />
                                <div>
                                    <p className="text-sm text-gray-500">Created</p>
                                    <p className="font-medium">
                                        {new Date(user.created_at).toLocaleDateString()}
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-center space-x-3">
                                {user.email_verified_at ? (
                                    <CheckCircle className="h-4 w-4 text-green-500" />
                                ) : (
                                    <XCircle className="h-4 w-4 text-red-500" />
                                )}
                                <div>
                                    <p className="text-sm text-gray-500">Email Status</p>
                                    <p className="font-medium">
                                        {user.email_verified_at ? 'Verified' : 'Unverified'}
                                    </p>
                                    {user.email_verified_at && (
                                        <p className="text-xs text-gray-500">
                                            Verified on {new Date(user.email_verified_at).toLocaleDateString()}
                                        </p>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Roles */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Shield className="h-5 w-5 mr-2" />
                                Roles ({user.roles.length})
                            </CardTitle>
                            <CardDescription>
                                Roles assigned to this user
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            {user.roles.length > 0 ? (
                                <div className="space-y-3">
                                    {user.roles.map((role) => (
                                        <div key={role.id} className="flex items-center justify-between p-3 border rounded-lg">
                                            <div>
                                                <Badge variant="secondary" className="mb-1">
                                                    {role.name}
                                                </Badge>
                                                <p className="text-xs text-gray-500">
                                                    {role.permissions.length} permissions
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-gray-500 text-center py-4">
                                    No roles assigned
                                </p>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Permissions */}
                <Card className="mt-6">
                    <CardHeader>
                        <CardTitle>Permissions ({uniquePermissions.length})</CardTitle>
                        <CardDescription>
                            All permissions granted to this user through their roles
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {uniquePermissions.length > 0 ? (
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                                {uniquePermissions.map((permission) => (
                                    <Badge key={permission.id} variant="outline" className="justify-start">
                                        {permission.name}
                                    </Badge>
                                ))}
                            </div>
                        ) : (
                            <p className="text-gray-500 text-center py-4">
                                No permissions granted
                            </p>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
