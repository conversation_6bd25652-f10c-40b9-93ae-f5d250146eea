import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
    Settings,
    CreditCard,
    Users,
    Shield,
    Database,
    Bell,
    Globe,
    Palette,
    FileSpreadsheet,
    Download,
    Upload
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface SettingsGroup {
    [key: string]: any;
}

interface Props {
    settings: {
        [groupName: string]: SettingsGroup;
    };
}

const settingsCategories = [
    {
        id: 'mpesa_id',
        title: 'M-Pesa ID Management',
        description: 'Configure customer M-Pesa ID generation and assignment rules',
        icon: CreditCard,
        href: '/admin/settings/mpesa-id',
        color: 'bg-green-500',
        available: true,
    },
    {
        id: 'users',
        title: 'User Management',
        description: 'Manage user roles, permissions, and access controls',
        icon: Users,
        href: '/admin/settings/users',
        color: 'bg-blue-500',
        available: false,
    },
    {
        id: 'security',
        title: 'Security Settings',
        description: 'Configure authentication, encryption, and security policies',
        icon: Shield,
        href: '/admin/settings/security',
        color: 'bg-red-500',
        available: false,
    },
    {
        id: 'database',
        title: 'Database Settings',
        description: 'Manage database connections, backups, and maintenance',
        icon: Database,
        href: '/admin/settings/database',
        color: 'bg-purple-500',
        available: false,
    },
    {
        id: 'notifications',
        title: 'Notifications',
        description: 'Configure email, SMS, and system notification settings',
        icon: Bell,
        href: '/admin/settings/notifications',
        color: 'bg-yellow-500',
        available: false,
    },
    {
        id: 'system',
        title: 'System Settings',
        description: 'General system configuration and preferences',
        icon: Globe,
        href: '/admin/settings/system',
        color: 'bg-indigo-500',
        available: false,
    },
    {
        id: 'appearance',
        title: 'Appearance',
        description: 'Customize system theme, branding, and UI preferences',
        icon: Palette,
        href: '/admin/settings/appearance',
        color: 'bg-pink-500',
        available: false,
    },
];

export default function AdminSettingsIndex({ settings }: Props) {
    return (
        <AppLayout>
            <Head title="Admin Settings" />

            <div className="page-container">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Admin Settings</h1>
                        <p className="text-gray-600 mt-2">
                            Manage system configuration and administrative settings
                        </p>
                    </div>
                    <div className="flex items-center space-x-2">
                        <Settings className="h-8 w-8 text-gray-400" />
                    </div>
                </div>

                {/* Settings Categories Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {settingsCategories.map((category) => {
                        const Icon = category.icon;
                        const settingsCount = settings[category.id] ? Object.keys(settings[category.id]).length : 0;

                        return (
                            <Card key={category.id} className="hover:shadow-lg transition-shadow duration-200">
                                <CardHeader className="pb-3">
                                    <div className="flex items-center justify-between">
                                        <div className={`p-2 rounded-lg ${category.color} bg-opacity-10`}>
                                            <Icon className={`h-6 w-6 ${category.color.replace('bg-', 'text-')}`} />
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            {category.available ? (
                                                <Badge variant="default" className="bg-green-100 text-green-800">
                                                    Available
                                                </Badge>
                                            ) : (
                                                <Badge variant="secondary">
                                                    Coming Soon
                                                </Badge>
                                            )}
                                        </div>
                                    </div>
                                    <CardTitle className="text-lg">{category.title}</CardTitle>
                                    <CardDescription className="text-sm">
                                        {category.description}
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="pt-0">
                                    <div className="flex items-center justify-between">
                                        <div className="text-sm text-gray-500">
                                            {settingsCount > 0 ? `${settingsCount} settings` : 'No settings configured'}
                                        </div>
                                        {category.available ? (
                                            <Button asChild size="sm">
                                                <Link href={category.href}>
                                                    Configure
                                                </Link>
                                            </Button>
                                        ) : (
                                            <Button size="sm" disabled>
                                                Configure
                                            </Button>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        );
                    })}
                </div>

                {/* Quick Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <div className="p-2 bg-blue-100 rounded-lg">
                                    <Settings className="h-4 w-4 text-blue-600" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Total Settings</p>
                                    <p className="text-2xl font-bold text-gray-900">
                                        {Object.values(settings).reduce((total, group) => total + Object.keys(group).length, 0)}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <div className="p-2 bg-green-100 rounded-lg">
                                    <CreditCard className="h-4 w-4 text-green-600" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-gray-600">M-Pesa Settings</p>
                                    <p className="text-2xl font-bold text-gray-900">
                                        {settings.mpesa_id ? Object.keys(settings.mpesa_id).length : 0}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <div className="p-2 bg-purple-100 rounded-lg">
                                    <Shield className="h-4 w-4 text-purple-600" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Security Level</p>
                                    <p className="text-2xl font-bold text-gray-900">High</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <div className="p-2 bg-yellow-100 rounded-lg">
                                    <Globe className="h-4 w-4 text-yellow-600" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-gray-600">System Status</p>
                                    <p className="text-2xl font-bold text-green-600">Online</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Import Documentation Section */}
                <div className="mt-8">
                    <h2 className="text-xl font-semibold mb-4">Data Import</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Card className="hover:shadow-md transition-shadow">
                            <CardHeader className="pb-3">
                                <CardTitle className="flex items-center text-lg">
                                    <FileSpreadsheet className="h-5 w-5 mr-2 text-green-600" />
                                    Import Documentation
                                </CardTitle>
                                <CardDescription>
                                    Customer data import template and guidelines
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-sm text-gray-600 mb-4">
                                    Download Excel template and view comprehensive import documentation
                                </p>
                                <div className="space-y-2">
                                    <Button variant="outline" className="w-full">
                                        <Link href={route('admin.import.documentation')} className="flex items-center">
                                            View Documentation
                                        </Link>
                                    </Button>
                                    <Button className="w-full">
                                        <Link href={route('admin.import.upload')} className="flex items-center">
                                            <Upload className="h-4 w-4 mr-2" />
                                            Upload Import File
                                        </Link>
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="hover:shadow-md transition-shadow">
                            <CardHeader className="pb-3">
                                <CardTitle className="flex items-center text-lg">
                                    <Download className="h-5 w-5 mr-2 text-blue-600" />
                                    Download Template
                                </CardTitle>
                                <CardDescription>
                                    Get the latest import template in Excel or CSV format
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-sm text-gray-600 mb-4">
                                    Download the standardized template for customer data import
                                </p>
                                <div className="space-y-2">
                                    <Button
                                        variant="outline"
                                        className="w-full"
                                        onClick={() => {
                                            const link = document.createElement('a');
                                            link.href = route('admin.import.template.download');
                                            link.download = 'customer_import_template.xlsx';
                                            document.body.appendChild(link);
                                            link.click();
                                            document.body.removeChild(link);
                                        }}
                                    >
                                        Download Excel (.xlsx)
                                    </Button>
                                    <Button
                                        variant="outline"
                                        className="w-full"
                                        onClick={() => {
                                            const link = document.createElement('a');
                                            link.href = route('admin.import.template.download-csv');
                                            link.download = 'customer_import_template.csv';
                                            document.body.appendChild(link);
                                            link.click();
                                            document.body.removeChild(link);
                                        }}
                                    >
                                        Download CSV (Google Sheets)
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
