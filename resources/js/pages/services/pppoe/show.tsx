import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>List,
  TabsTrigger
} from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Wifi,
  WifiOff,
  Clock,
  User,
  Server,
  Activity,
  Download,
  Upload,
  AlertTriangle,
  CheckCircle2,
  XCircle
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import AppLayout from '@/layouts/app-layout';

export default function ShowPppoeService({ service, activeSession }) {
  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'suspended':
        return <Badge className="bg-yellow-500">Suspended</Badge>;
      case 'terminated':
        return <Badge className="bg-red-500">Terminated</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  return (
    <AppLayout>
      <Head title={`PPPoE Service - ${service.username}`} />

      <div className="page-container">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-2">
              <Link href={route('services.pppoe.index')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to PPPoE Services
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">PPPoE Service Details</h1>
          </div>
          <div className="flex space-x-2">
            <Button asChild variant="outline">
              <Link href={route('services.pppoe.edit', service.id)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Service
              </Link>
            </Button>
            {service.status === 'active' && activeSession && (
              <Button asChild variant="outline" className="bg-yellow-50 text-yellow-700 hover:bg-yellow-100">
                <Link href={route('services.pppoe.disconnect', service.id)} method="post" as="button">
                  <WifiOff className="h-4 w-4 mr-2" />
                  Disconnect Session
                </Link>
              </Button>
            )}
            {service.status === 'active' ? (
              <Button asChild variant="outline" className="bg-yellow-50 text-yellow-700 hover:bg-yellow-100">
                <Link href={route('services.pppoe.update', service.id)} method="put" data={{ status: 'suspended' }} as="button">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Suspend Service
                </Link>
              </Button>
            ) : (
              <Button asChild variant="outline" className="bg-green-50 text-green-700 hover:bg-green-100">
                <Link href={route('services.pppoe.update', service.id)} method="put" data={{ status: 'active' }} as="button">
                  <CheckCircle2 className="h-4 w-4 mr-2" />
                  Activate Service
                </Link>
              </Button>
            )}
            <Button asChild variant="destructive">
              <Link href={route('services.pppoe.destroy', service.id)} method="delete" as="button">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Service
              </Link>
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <Tabs defaultValue="overview">
              <TabsList className="mb-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="session">Active Session</TabsTrigger>
                <TabsTrigger value="history">Connection History</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                {/* Service Status Card */}
                <Card>
                  <CardHeader>
                    <CardTitle>Service Status</CardTitle>
                    <CardDescription>Current status of the PPPoE service</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        {service.status === 'active' ? (
                          <Wifi className="h-8 w-8 text-green-500 mr-4" />
                        ) : (
                          <WifiOff className="h-8 w-8 text-yellow-500 mr-4" />
                        )}
                        <div>
                          <h3 className="text-lg font-medium">Status: {getStatusBadge(service.status)}</h3>
                          <p className="text-gray-500">
                            {service.status === 'active'
                              ? 'Service is active and available for connection'
                              : 'Service is suspended and cannot be used'}
                          </p>
                        </div>
                      </div>
                      <div>
                        {activeSession ? (
                          <Badge className="bg-green-500">Currently Connected</Badge>
                        ) : (
                          <Badge className="bg-gray-500">Not Connected</Badge>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Service Details Card */}
                <Card>
                  <CardHeader>
                    <CardTitle>Service Details</CardTitle>
                    <CardDescription>PPPoE service configuration</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Username</h3>
                          <p className="font-mono">{service.username}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Password</h3>
                          <p className="font-mono">••••••••</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Service Profile</h3>
                          <p>{service.service_profile || 'default'}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">IP Address</h3>
                          <p>{service.ip_address || 'Dynamic (not connected)'}</p>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Created</h3>
                          <p>{new Date(service.created_at).toLocaleString()}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Last Updated</h3>
                          <p>{new Date(service.updated_at).toLocaleString()}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Last Connected</h3>
                          <p>{service.last_connected ? new Date(service.last_connected).toLocaleString() : 'Never'}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Last Disconnected</h3>
                          <p>{service.last_disconnected ? new Date(service.last_disconnected).toLocaleString() : 'Never'}</p>
                        </div>
                      </div>
                    </div>

                    {service.comment && (
                      <div className="mt-4 p-3 bg-gray-50 rounded-md">
                        <h3 className="text-sm font-medium text-gray-500">Comment</h3>
                        <p className="whitespace-pre-line">{service.comment}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Bandwidth Plan Card */}
                {service.bandwidth_plan && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Bandwidth Plan</CardTitle>
                      <CardDescription>Bandwidth limitations for this service</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-lg font-medium">{service.bandwidth_plan.name}</h3>
                          <p className="text-gray-500">{service.bandwidth_plan.description}</p>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="text-center">
                            <Download className="h-5 w-5 mx-auto text-blue-500" />
                            <p className="text-sm font-medium">{service.bandwidth_plan.download_speed} Kbps</p>
                            <p className="text-xs text-gray-500">Download</p>
                          </div>
                          <div className="text-center">
                            <Upload className="h-5 w-5 mx-auto text-green-500" />
                            <p className="text-sm font-medium">{service.bandwidth_plan.upload_speed} Kbps</p>
                            <p className="text-xs text-gray-500">Upload</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="session">
                <Card>
                  <CardHeader>
                    <CardTitle>Active Session</CardTitle>
                    <CardDescription>Current PPPoE session information</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {activeSession ? (
                      <div className="space-y-6">
                        <div className="flex items-center">
                          <Activity className="h-8 w-8 text-green-500 mr-4" />
                          <div>
                            <h3 className="text-lg font-medium">Active Connection</h3>
                            <p className="text-gray-500">
                              Connected since: {activeSession.uptime ? activeSession.uptime : 'Unknown'}
                            </p>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-4">
                            <div>
                              <h3 className="text-sm font-medium text-gray-500">IP Address</h3>
                              <p className="font-mono">{activeSession.address || 'Unknown'}</p>
                            </div>
                            <div>
                              <h3 className="text-sm font-medium text-gray-500">Caller ID</h3>
                              <p className="font-mono">{activeSession['caller-id'] || 'Unknown'}</p>
                            </div>
                            <div>
                              <h3 className="text-sm font-medium text-gray-500">Service</h3>
                              <p>{activeSession.service || 'Unknown'}</p>
                            </div>
                          </div>
                          <div className="space-y-4">
                            <div>
                              <h3 className="text-sm font-medium text-gray-500">Encoding</h3>
                              <p>{activeSession.encoding || 'Unknown'}</p>
                            </div>
                            <div>
                              <h3 className="text-sm font-medium text-gray-500">Session ID</h3>
                              <p className="font-mono">{activeSession['session-id'] || 'Unknown'}</p>
                            </div>
                            <div>
                              <h3 className="text-sm font-medium text-gray-500">Uptime</h3>
                              <p>{activeSession.uptime || 'Unknown'}</p>
                            </div>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 bg-blue-50 rounded-md">
                            <h3 className="text-sm font-medium text-blue-700 mb-2">Download</h3>
                            <div className="flex items-center">
                              <Download className="h-5 w-5 text-blue-500 mr-2" />
                              <div>
                                <p className="font-medium">{activeSession['download-rate'] || '0'} bps</p>
                                <p className="text-sm text-gray-500">Current Rate</p>
                              </div>
                            </div>
                          </div>
                          <div className="p-4 bg-green-50 rounded-md">
                            <h3 className="text-sm font-medium text-green-700 mb-2">Upload</h3>
                            <div className="flex items-center">
                              <Upload className="h-5 w-5 text-green-500 mr-2" />
                              <div>
                                <p className="font-medium">{activeSession['upload-rate'] || '0'} bps</p>
                                <p className="text-sm text-gray-500">Current Rate</p>
                              </div>
                            </div>
                          </div>
                        </div>

                        <Button asChild variant="outline" className="w-full">
                          <Link href={route('services.pppoe.disconnect', service.id)} method="post" as="button">
                            <WifiOff className="h-4 w-4 mr-2" />
                            Disconnect Session
                          </Link>
                        </Button>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <WifiOff className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                        <h3 className="text-lg font-medium">No Active Session</h3>
                        <p className="text-gray-500 max-w-md mx-auto mt-2">
                          The PPPoE user is not currently connected. When they connect, session information will appear here.
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="history">
                <Card>
                  <CardHeader>
                    <CardTitle>Connection History</CardTitle>
                    <CardDescription>Recent connection history for this PPPoE service</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {/* This would typically be populated from an API call */}
                    <div className="text-center py-8">
                      <Clock className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                      <h3 className="text-lg font-medium">Connection History Not Available</h3>
                      <p className="text-gray-500 max-w-md mx-auto mt-2">
                        Detailed connection history is not available in this view. Connection logs can be viewed directly on the MikroTik device.
                      </p>
                      {service.device && (
                        <Button asChild variant="outline" className="mt-4">
                          <Link href={route('network.devices.show', service.device.id)}>
                            View Device Details
                          </Link>
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          <div className="space-y-6">
            {/* Customer Card */}
            <Card>
              <CardHeader>
                <CardTitle>Customer</CardTitle>
                <CardDescription>Customer information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center">
                  <User className="h-10 w-10 text-primary bg-primary/10 p-2 rounded-full mr-4" />
                  <div>
                    <Link href={route('customers.show', service.customer.id)} className="text-lg font-medium text-blue-500 hover:underline">
                      {service.customer.name}
                    </Link>
                    <p className="text-gray-500">{service.customer.email}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-500">Phone</h3>
                  <p>{service.customer.phone || 'Not provided'}</p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-500">Address</h3>
                  <p className="whitespace-pre-line">{service.customer.full_address || 'Not provided'}</p>
                </div>

                <Button asChild variant="outline" className="w-full">
                  <Link href={route('customers.show', service.customer.id)}>
                    View Customer Details
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* Subscription Card */}
            <Card>
              <CardHeader>
                <CardTitle>Subscription</CardTitle>
                <CardDescription>Subscription information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {service.subscription ? (
                  <>
                    <div className="flex items-center">
                      <div>
                        <Link href={route('subscriptions.show', service.subscription.id)} className="text-lg font-medium text-blue-500 hover:underline">
                          {service.subscription.name}
                        </Link>
                        <p className="text-gray-500">${service.subscription.price} / {service.subscription.billing_cycle}</p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Status</h3>
                      <p>{service.subscription.status}</p>
                    </div>

                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Start Date</h3>
                      <p>{new Date(service.subscription.start_date).toLocaleDateString()}</p>
                    </div>

                    {service.subscription.end_date && (
                      <div className="space-y-2">
                        <h3 className="text-sm font-medium text-gray-500">End Date</h3>
                        <p>{new Date(service.subscription.end_date).toLocaleDateString()}</p>
                      </div>
                    )}

                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Next Billing</h3>
                      <p>{new Date(service.subscription.next_billing_date).toLocaleDateString()}</p>
                    </div>

                    <Button asChild variant="outline" className="w-full">
                      <Link href={route('subscriptions.show', service.subscription.id)}>
                        View Subscription Details
                      </Link>
                    </Button>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-gray-400 mb-4">
                      <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium">No Subscription</h3>
                    <p className="text-gray-500 max-w-md mx-auto mt-2">
                      This service was created without a subscription. It operates as a standalone PPPoE service.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Router Device Card */}
            <Card>
              <CardHeader>
                <CardTitle>Router Device</CardTitle>
                <CardDescription>Device hosting this PPPoE service</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {service.device ? (
                  <>
                    <div className="flex items-center">
                      <Server className="h-10 w-10 text-primary bg-primary/10 p-2 rounded-full mr-4" />
                      <div>
                        <Link href={route('network.devices.show', service.device.id)} className="text-lg font-medium text-blue-500 hover:underline">
                          {service.device.name}
                        </Link>
                        <p className="text-gray-500">{service.device.ip_address}</p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Type</h3>
                      <p>{service.device.type}</p>
                    </div>

                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Model</h3>
                      <p>{service.device.model || 'Not specified'}</p>
                    </div>

                    <Button asChild variant="outline" className="w-full">
                      <Link href={route('network.devices.show', service.device.id)}>
                        View Device Details
                      </Link>
                    </Button>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-gray-400 mb-4">
                      <Server className="h-12 w-12 mx-auto" />
                    </div>
                    <h3 className="text-lg font-medium">No Device Assigned</h3>
                    <p className="text-gray-500 max-w-md mx-auto mt-2">
                      This service does not have a router device assigned. Please contact support.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
