<?php

use Illuminate\Support\Facades\Broadcast;

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

// Static IP Provisioning Channels
Broadcast::channel('static-ip-provisioning.{serviceId}', function ($user, $serviceId) {
    // Allow access if user has permission to view services
    return $user->hasPermissionTo('view services');
});

Broadcast::channel('static-ip-dashboard', function ($user) {
    // Allow access if user has permission to view services (for admin dashboard)
    return $user->hasPermissionTo('view services') ? [
        'id' => $user->id,
        'name' => $user->name,
    ] : null;
});
