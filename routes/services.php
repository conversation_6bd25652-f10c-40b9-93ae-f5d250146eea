<?php

use App\Http\Controllers\Services\IpPoolController;
use App\Http\Controllers\Services\PppoeServiceController;
use App\Http\Controllers\Services\StaticIpServiceController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified'])->group(function () {
    // Main Services Dashboard with permission middleware
    Route::get('services', [App\Http\Controllers\Services\ServiceController::class, 'index'])->middleware('permission:view services')->name('services.index');

    // PPPoE Services with permission middleware
    Route::get('services/pppoe', [PppoeServiceController::class, 'index'])->middleware('permission:view services')->name('services.pppoe.index');
    Route::get('services/pppoe/create', [PppoeServiceController::class, 'create'])->middleware('permission:create services')->name('services.pppoe.create');
    Route::post('services/pppoe', [PppoeServiceController::class, 'store'])->middleware('permission:create services')->name('services.pppoe.store');
    Route::get('services/pppoe/{service}', [PppoeServiceController::class, 'show'])->middleware('permission:view services')->name('services.pppoe.show');
    Route::get('services/pppoe/{service}/edit', [PppoeServiceController::class, 'edit'])->middleware('permission:edit services')->name('services.pppoe.edit');
    Route::put('services/pppoe/{service}', [PppoeServiceController::class, 'update'])->middleware('permission:edit services')->name('services.pppoe.update');
    Route::delete('services/pppoe/{service}', [PppoeServiceController::class, 'destroy'])->middleware('permission:delete services')->name('services.pppoe.destroy');
    Route::post('services/pppoe/{service}/disconnect', [PppoeServiceController::class, 'disconnect'])->middleware('permission:edit services')->name('services.pppoe.disconnect');

    // Static IP Services with permission middleware
    Route::get('services/static-ip', [StaticIpServiceController::class, 'index'])->middleware('permission:view services')->name('services.static-ip.index');
    Route::get('services/static-ip/create', [StaticIpServiceController::class, 'create'])->middleware('permission:create services')->name('services.static-ip.create');
    Route::post('services/static-ip', [StaticIpServiceController::class, 'store'])->middleware('permission:create services')->name('services.static-ip.store');
    Route::get('services/static-ip/{service}', [StaticIpServiceController::class, 'show'])->middleware('permission:view services')->name('services.static-ip.show');
    Route::get('services/static-ip/{service}/edit', [StaticIpServiceController::class, 'edit'])->middleware('permission:edit services')->name('services.static-ip.edit');
    Route::put('services/static-ip/{service}', [StaticIpServiceController::class, 'update'])->middleware('permission:edit services')->name('services.static-ip.update');
    Route::delete('services/static-ip/{service}', [StaticIpServiceController::class, 'destroy'])->middleware('permission:delete services')->name('services.static-ip.destroy');

    // IP Pools with permission middleware
    Route::get('services/ip-pools', [IpPoolController::class, 'index'])->middleware('permission:view services')->name('services.ip-pools.index');
    Route::get('services/ip-pools/create', [IpPoolController::class, 'create'])->middleware('permission:manage ip pools')->name('services.ip-pools.create');
    Route::post('services/ip-pools', [IpPoolController::class, 'store'])->middleware('permission:manage ip pools')->name('services.ip-pools.store');
    Route::get('services/ip-pools/{ipPool}', [IpPoolController::class, 'show'])->middleware('permission:view services')->name('services.ip-pools.show');
    Route::get('services/ip-pools/{ipPool}/edit', [IpPoolController::class, 'edit'])->middleware('permission:manage ip pools')->name('services.ip-pools.edit');
    Route::put('services/ip-pools/{ipPool}', [IpPoolController::class, 'update'])->middleware('permission:manage ip pools')->name('services.ip-pools.update');
    Route::delete('services/ip-pools/{ipPool}', [IpPoolController::class, 'destroy'])->middleware('permission:manage ip pools')->name('services.ip-pools.destroy');

    // Device interfaces for IP pools with permission middleware
    Route::get('services/ip-pools/device/{device}/interfaces', [IpPoolController::class, 'getDeviceInterfaces'])->middleware('permission:view services')->name('services.ip-pools.device-interfaces');

    // API Routes for Services with permission middleware
    Route::prefix('api')->group(function () {
        // PPPoE Services API
        Route::get('services/pppoe', [PppoeServiceController::class, 'apiIndex'])->middleware('permission:view services')->name('api.services.pppoe.index');
        Route::get('services/pppoe/{service}', [PppoeServiceController::class, 'apiShow'])->middleware('permission:view services')->name('api.services.pppoe.show');
        Route::post('services/pppoe/{service}/status', [PppoeServiceController::class, 'apiUpdateStatus'])->middleware('permission:edit services')->name('api.services.pppoe.status');

        // Static IP Services API
        Route::get('services/static-ip', [StaticIpServiceController::class, 'apiIndex'])->middleware('permission:view services')->name('api.services.static-ip.index');
        Route::get('services/static-ip/{service}', [StaticIpServiceController::class, 'apiShow'])->middleware('permission:view services')->name('api.services.static-ip.show');
        Route::post('services/static-ip/{service}/status', [StaticIpServiceController::class, 'apiUpdateStatus'])->middleware('permission:edit services')->name('api.services.static-ip.status');

        // Streamlined Static IP Service creation API
        Route::get('services/static-ip/customer/{customer}/data', [StaticIpServiceController::class, 'getCustomerData'])->middleware('permission:view services')->name('api.services.static-ip.customer-data');
        Route::get('services/static-ip/ip-pool/{ipPool}/next-ip', [StaticIpServiceController::class, 'getNextAvailableIp'])->middleware('permission:view services')->name('api.services.static-ip.next-ip');
        Route::get('services/static-ip/ip-pool/{ipPool}/available-ips', [StaticIpServiceController::class, 'getAvailableIps'])->middleware('permission:view services')->name('api.services.static-ip.available-ips');

        // Streamlined PPPoE Service creation API
        Route::get('services/pppoe/customer/{customer}/data', [PppoeServiceController::class, 'getCustomerData'])->middleware('permission:view services')->name('api.services.pppoe.customer-data');
        Route::get('services/pppoe/customer/{customer}/credentials', [PppoeServiceController::class, 'generateCredentials'])->middleware('permission:create services')->name('api.services.pppoe.generate-credentials');

        // IP Pools API
        Route::get('services/ip-pools', [IpPoolController::class, 'apiIndex'])->middleware('permission:view services')->name('api.services.ip-pools.index');
        Route::get('services/ip-pools/{ipPool}', [IpPoolController::class, 'apiShow'])->middleware('permission:view services')->name('api.services.ip-pools.show');
        Route::get('services/ip-pools/{ipPool}/next-ip', [IpPoolController::class, 'apiGetNextAvailableIp'])->middleware('permission:view services')->name('api.services.ip-pools.next-ip');
    });
});
