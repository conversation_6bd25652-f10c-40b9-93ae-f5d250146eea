<?php

use App\Http\Controllers\Services\IpPoolController;
use App\Http\Controllers\Services\PppoeServiceController;
use App\Http\Controllers\Services\StaticIpServiceController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified'])->group(function () {
    // Main Services Dashboard
    Route::get('services', [App\Http\Controllers\Services\ServiceController::class, 'index'])->name('services.index');

    // PPPoE Services
    Route::get('services/pppoe', [PppoeServiceController::class, 'index'])->name('services.pppoe.index');
    Route::get('services/pppoe/create', [PppoeServiceController::class, 'create'])->name('services.pppoe.create');
    Route::post('services/pppoe', [PppoeServiceController::class, 'store'])->name('services.pppoe.store');
    Route::get('services/pppoe/{service}', [PppoeServiceController::class, 'show'])->name('services.pppoe.show');
    Route::get('services/pppoe/{service}/edit', [PppoeServiceController::class, 'edit'])->name('services.pppoe.edit');
    Route::put('services/pppoe/{service}', [PppoeServiceController::class, 'update'])->name('services.pppoe.update');
    Route::delete('services/pppoe/{service}', [PppoeServiceController::class, 'destroy'])->name('services.pppoe.destroy');
    Route::post('services/pppoe/{service}/disconnect', [PppoeServiceController::class, 'disconnect'])->name('services.pppoe.disconnect');

    // Static IP Services
    Route::get('services/static-ip', [StaticIpServiceController::class, 'index'])->name('services.static-ip.index');
    Route::get('services/static-ip/create', [StaticIpServiceController::class, 'create'])->name('services.static-ip.create');
    Route::post('services/static-ip', [StaticIpServiceController::class, 'store'])->name('services.static-ip.store');
    Route::get('services/static-ip/{service}', [StaticIpServiceController::class, 'show'])->name('services.static-ip.show');
    Route::get('services/static-ip/{service}/edit', [StaticIpServiceController::class, 'edit'])->name('services.static-ip.edit');
    Route::put('services/static-ip/{service}', [StaticIpServiceController::class, 'update'])->name('services.static-ip.update');
    Route::delete('services/static-ip/{service}', [StaticIpServiceController::class, 'destroy'])->name('services.static-ip.destroy');

    // IP Pools
    Route::get('services/ip-pools', [IpPoolController::class, 'index'])->name('services.ip-pools.index');
    Route::get('services/ip-pools/create', [IpPoolController::class, 'create'])->name('services.ip-pools.create');
    Route::post('services/ip-pools', [IpPoolController::class, 'store'])->name('services.ip-pools.store');
    Route::get('services/ip-pools/{ipPool}', [IpPoolController::class, 'show'])->name('services.ip-pools.show');
    Route::get('services/ip-pools/{ipPool}/edit', [IpPoolController::class, 'edit'])->name('services.ip-pools.edit');
    Route::put('services/ip-pools/{ipPool}', [IpPoolController::class, 'update'])->name('services.ip-pools.update');
    Route::delete('services/ip-pools/{ipPool}', [IpPoolController::class, 'destroy'])->name('services.ip-pools.destroy');

    // Device interfaces for IP pools
    Route::get('services/ip-pools/device/{device}/interfaces', [IpPoolController::class, 'getDeviceInterfaces'])->name('services.ip-pools.device-interfaces');

    // API Routes for Services
    Route::prefix('api')->group(function () {
        // PPPoE Services API
        Route::get('services/pppoe', [PppoeServiceController::class, 'apiIndex'])->name('api.services.pppoe.index');
        Route::get('services/pppoe/{service}', [PppoeServiceController::class, 'apiShow'])->name('api.services.pppoe.show');
        Route::post('services/pppoe/{service}/status', [PppoeServiceController::class, 'apiUpdateStatus'])->name('api.services.pppoe.status');

        // Static IP Services API
        Route::get('services/static-ip', [StaticIpServiceController::class, 'apiIndex'])->name('api.services.static-ip.index');
        Route::get('services/static-ip/{service}', [StaticIpServiceController::class, 'apiShow'])->name('api.services.static-ip.show');
        Route::post('services/static-ip/{service}/status', [StaticIpServiceController::class, 'apiUpdateStatus'])->name('api.services.static-ip.status');

        // Streamlined Static IP Service creation API
        Route::get('services/static-ip/customer/{customer}/data', [StaticIpServiceController::class, 'getCustomerData'])->name('api.services.static-ip.customer-data');
        Route::get('services/static-ip/ip-pool/{ipPool}/next-ip', [StaticIpServiceController::class, 'getNextAvailableIp'])->name('api.services.static-ip.next-ip');
        Route::get('services/static-ip/ip-pool/{ipPool}/available-ips', [StaticIpServiceController::class, 'getAvailableIps'])->name('api.services.static-ip.available-ips');

        // Streamlined PPPoE Service creation API
        Route::get('services/pppoe/customer/{customer}/data', [PppoeServiceController::class, 'getCustomerData'])->name('api.services.pppoe.customer-data');
        Route::get('services/pppoe/customer/{customer}/credentials', [PppoeServiceController::class, 'generateCredentials'])->name('api.services.pppoe.generate-credentials');

        // IP Pools API
        Route::get('services/ip-pools', [IpPoolController::class, 'apiIndex'])->name('api.services.ip-pools.index');
        Route::get('services/ip-pools/{ipPool}', [IpPoolController::class, 'apiShow'])->name('api.services.ip-pools.show');
        Route::get('services/ip-pools/{ipPool}/next-ip', [IpPoolController::class, 'apiGetNextAvailableIp'])->name('api.services.ip-pools.next-ip');
    });
});
