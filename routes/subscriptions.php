<?php

use App\Http\Controllers\Web\StreamlinedSubscriptionController;
use App\Http\Controllers\Web\SubscriptionController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified'])->group(function () {
    // Subscription routes
    Route::get('/subscriptions', [SubscriptionController::class, 'index'])->name('subscriptions.index');
    Route::get('/subscriptions/create', [SubscriptionController::class, 'create'])->name('subscriptions.create');
    Route::post('/subscriptions', [SubscriptionController::class, 'store'])->name('subscriptions.store');
    Route::get('/subscriptions/{subscription}', [SubscriptionController::class, 'show'])->name('subscriptions.show');
    Route::get('/subscriptions/{subscription}/edit', [SubscriptionController::class, 'edit'])->name('subscriptions.edit');
    Route::put('/subscriptions/{subscription}', [SubscriptionController::class, 'update'])->name('subscriptions.update');
    Route::put('/subscriptions/{subscription}/status', [SubscriptionController::class, 'updateStatus'])->name('subscriptions.update-status');
    Route::delete('/subscriptions/{subscription}', [SubscriptionController::class, 'destroy'])->name('subscriptions.destroy');

    // Streamlined subscription routes
    Route::get('/subscriptions/streamlined/create', [StreamlinedSubscriptionController::class, 'create'])->name('subscriptions.streamlined.create');
    Route::post('/subscriptions/streamlined', [StreamlinedSubscriptionController::class, 'store'])->name('subscriptions.streamlined.store');
    Route::get('/api/bandwidth-plans/{bandwidthPlan}', [StreamlinedSubscriptionController::class, 'getBandwidthPlan'])->name('api.bandwidth-plans.show');

    // Subscription invoices routes (placeholder for future implementation)
    Route::get('/subscriptions/{subscription}/invoices', function () {
        return redirect()->route('subscriptions.show', request()->route('subscription'));
    })->name('subscriptions.invoices.index');

    Route::get('/subscriptions/{subscription}/invoices/create', function () {
        return redirect()->route('subscriptions.show', request()->route('subscription'));
    })->name('subscriptions.invoices.create');
});
