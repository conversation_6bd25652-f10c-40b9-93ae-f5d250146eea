<?php

use App\Http\Controllers\Bandwidth\BandwidthPlanController;
use App\Http\Controllers\Bandwidth\BandwidthUsageController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::middleware(['auth', 'verified'])->group(function () {
    // Main Bandwidth Dashboard
    Route::get('bandwidth', function () {
        return Inertia::render('bandwidth/index');
    })->name('bandwidth.index');

    // Bandwidth Plans
    Route::get('bandwidth/plans', [BandwidthPlanController::class, 'index'])->name('bandwidth.plans.index');

    Route::get('bandwidth/plans/create', function () {
        return Inertia::render('bandwidth/plans/create');
    })->name('bandwidth.plans.create');

    Route::post('bandwidth/plans', [BandwidthPlanController::class, 'store'])->name('bandwidth.plans.store');

    Route::get('bandwidth/plans/{plan}', function ($plan) {
        return Inertia::render('bandwidth/plans/show', [
            'planId' => $plan,
        ]);
    })->name('bandwidth.plans.show');

    Route::get('bandwidth/plans/{plan}/edit', function ($plan) {
        return Inertia::render('bandwidth/plans/edit', [
            'planId' => $plan,
        ]);
    })->name('bandwidth.plans.edit');

    // Bandwidth Policies
    Route::get('bandwidth/policies', function () {
        return Inertia::render('bandwidth/policies/index');
    })->name('bandwidth.policies.index');

    Route::get('bandwidth/policies/create', function () {
        return Inertia::render('bandwidth/policies/create');
    })->name('bandwidth.policies.create');

    Route::get('bandwidth/policies/{policy}', function ($policy) {
        return Inertia::render('bandwidth/policies/show', [
            'policyId' => $policy,
        ]);
    })->name('bandwidth.policies.show');

    Route::get('bandwidth/policies/{policy}/edit', function ($policy) {
        return Inertia::render('bandwidth/policies/edit', [
            'policyId' => $policy,
        ]);
    })->name('bandwidth.policies.edit');

    Route::get('bandwidth/policies/{policy}/rules', function ($policy) {
        return Inertia::render('bandwidth/policies/rules', [
            'policyId' => $policy,
        ]);
    })->name('bandwidth.policies.rules');

    Route::get('bandwidth/policies/{policy}/parameters', function ($policy) {
        return Inertia::render('bandwidth/policies/parameters', [
            'policyId' => $policy,
        ]);
    })->name('bandwidth.policies.parameters');

    // Bandwidth Rules
    Route::get('bandwidth/rules', function () {
        return Inertia::render('bandwidth/rules/index');
    })->name('bandwidth.rules.index');

    Route::get('bandwidth/rules/create', function () {
        return Inertia::render('bandwidth/rules/create');
    })->name('bandwidth.rules.create');

    Route::get('bandwidth/rules/{rule}', function ($rule) {
        return Inertia::render('bandwidth/rules/show', [
            'ruleId' => $rule,
        ]);
    })->name('bandwidth.rules.show');

    Route::get('bandwidth/rules/{rule}/edit', function ($rule) {
        return Inertia::render('bandwidth/rules/edit', [
            'ruleId' => $rule,
        ]);
    })->name('bandwidth.rules.edit');

    // Bandwidth Quotas
    Route::get('bandwidth/quotas', function () {
        return Inertia::render('bandwidth/quotas/index');
    })->name('bandwidth.quotas.index');

    Route::get('bandwidth/quotas/create', function () {
        return Inertia::render('bandwidth/quotas/create');
    })->name('bandwidth.quotas.create');

    Route::get('bandwidth/quotas/{quota}', function ($quota) {
        return Inertia::render('bandwidth/quotas/show', [
            'quotaId' => $quota,
        ]);
    })->name('bandwidth.quotas.show');

    Route::get('bandwidth/quotas/{quota}/edit', function ($quota) {
        return Inertia::render('bandwidth/quotas/edit', [
            'quotaId' => $quota,
        ]);
    })->name('bandwidth.quotas.edit');

    // Bandwidth Usage
    Route::get('bandwidth/usage', function () {
        return Inertia::render('bandwidth/usage/index');
    })->name('bandwidth.usage.index');

    Route::get('bandwidth/usage/dashboard', function () {
        return Inertia::render('bandwidth/usage/dashboard');
    })->name('bandwidth.usage.dashboard');

    Route::get('bandwidth/usage/{usage}', function ($usage) {
        return Inertia::render('bandwidth/usage/show', [
            'usageId' => $usage,
        ]);
    })->name('bandwidth.usage.show');

    Route::get('bandwidth/usage/export', function () {
        return Inertia::render('bandwidth/usage/export');
    })->name('bandwidth.usage.export');

    // Bandwidth Assignments
    Route::get('bandwidth/assignments', function () {
        return Inertia::render('bandwidth/assignments/index');
    })->name('bandwidth.assignments.index');

    Route::get('bandwidth/assignments/create', function () {
        return Inertia::render('bandwidth/assignments/create');
    })->name('bandwidth.assignments.create');

    Route::get('bandwidth/assignments/{assignment}', function ($assignment) {
        return Inertia::render('bandwidth/assignments/show', [
            'assignmentId' => $assignment,
        ]);
    })->name('bandwidth.assignments.show');

    Route::get('bandwidth/assignments/{assignment}/edit', function ($assignment) {
        return Inertia::render('bandwidth/assignments/edit', [
            'assignmentId' => $assignment,
        ]);
    })->name('bandwidth.assignments.edit');
});

// API Routes for Bandwidth Plans
Route::middleware(['auth', 'verified'])->prefix('api')->group(function () {
    Route::apiResource('bandwidth/plans', BandwidthPlanController::class);
    Route::get('bandwidth/plans/{plan}/assignments', [BandwidthPlanController::class, 'assignments']);
    Route::post('bandwidth/plans/{plan}/assign', [BandwidthPlanController::class, 'assign']);
    Route::delete('bandwidth/plans/{plan}/assignments', [BandwidthPlanController::class, 'removeAssignment']);
    Route::get('bandwidth/plans-mbps', [BandwidthPlanController::class, 'plansInMbps']);
    Route::post('bandwidth/plans/{plan}/clone', [BandwidthPlanController::class, 'clone']);
    Route::get('bandwidth/statistics', [BandwidthPlanController::class, 'statistics']);
    Route::get('bandwidth/stats', [BandwidthPlanController::class, 'dashboardStats']);
});

// API Routes for Bandwidth Usage
Route::middleware(['auth', 'verified'])->prefix('api')->group(function () {
    Route::get('bandwidth/usage', [BandwidthUsageController::class, 'index']);
    Route::post('bandwidth/usage', [BandwidthUsageController::class, 'store']);

    // Specific routes must come BEFORE parameterized routes
    Route::post('bandwidth/usage/record', [BandwidthUsageController::class, 'recordUsage']);
    Route::get('bandwidth/usage/get', [BandwidthUsageController::class, 'getUsage']);
    Route::get('bandwidth/usage/customer/{customer}', [BandwidthUsageController::class, 'getUsage']); // Alternative route
    Route::get('bandwidth/usage/top-users', [BandwidthUsageController::class, 'topUsers']);
    Route::get('bandwidth/usage/statistics', [BandwidthUsageController::class, 'statistics']);
    Route::get('bandwidth/usage/dashboard', [BandwidthUsageController::class, 'dashboard']);

    // Parameterized routes come AFTER specific routes
    Route::get('bandwidth/usage/{usage}', [BandwidthUsageController::class, 'show']);
    Route::put('bandwidth/usage/{usage}', [BandwidthUsageController::class, 'update']);
    Route::delete('bandwidth/usage/{usage}', [BandwidthUsageController::class, 'destroy']);
});

// Queue Usage Routes (for all MikroTik queues)
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('bandwidth/queue-usage', [\App\Http\Controllers\Bandwidth\QueueUsageController::class, 'index'])->name('bandwidth.queue-usage.index');
    Route::get('bandwidth/queue-usage/export', [\App\Http\Controllers\Bandwidth\QueueUsageController::class, 'export'])->name('bandwidth.queue-usage.export');
});

// API Routes for Queue Usage
Route::middleware(['auth', 'verified'])->prefix('api')->group(function () {
    Route::get('bandwidth/queue-usage/statistics', [\App\Http\Controllers\Bandwidth\QueueUsageController::class, 'statistics']);
});
