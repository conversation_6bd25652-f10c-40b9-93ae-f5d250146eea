<?php

use App\Http\Controllers\Web\CustomerController;
use App\Http\Controllers\Web\CustomerInvoiceController;
use App\Http\Controllers\Web\CustomerSubscriptionController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified'])->group(function () {
    // Customer routes with permission middleware
    Route::get('/customers', [CustomerController::class, 'index'])->middleware('permission:view customers')->name('customers.index');
    Route::get('/customers/create', [CustomerController::class, 'create'])->middleware('permission:create customers')->name('customers.create');
    Route::post('/customers', [CustomerController::class, 'store'])->middleware('permission:create customers')->name('customers.store');
    Route::get('/customers/{customer}', [CustomerController::class, 'show'])->middleware('permission:view customers')->name('customers.show');
    Route::get('/customers/{customer}/edit', [CustomerController::class, 'edit'])->middleware('permission:edit customers')->name('customers.edit');
    Route::put('/customers/{customer}', [CustomerController::class, 'update'])->middleware('permission:edit customers')->name('customers.update');
    Route::delete('/customers/{customer}', [CustomerController::class, 'destroy'])->middleware('permission:delete customers')->name('customers.destroy');
    Route::post('/customers/{customer}/generate-mpesa-id', [CustomerController::class, 'generateMpesaId'])->middleware('permission:edit customers')->name('customers.generate-mpesa-id');

    // Customer subscriptions routes with permission middleware
    Route::get('/customers/{customer}/subscriptions', [CustomerSubscriptionController::class, 'index'])->middleware('permission:view subscriptions')->name('customers.subscriptions.index');
    Route::get('/customers/{customer}/subscriptions/create', [CustomerSubscriptionController::class, 'create'])->middleware('permission:create subscriptions')->name('customers.subscriptions.create');
    Route::post('/customers/{customer}/subscriptions', [CustomerSubscriptionController::class, 'store'])->middleware('permission:create subscriptions')->name('customers.subscriptions.store');

    // Customer invoices routes with permission middleware
    Route::get('/customers/{customer}/invoices', [CustomerInvoiceController::class, 'index'])->middleware('permission:view invoices')->name('customers.invoices.index');
    Route::get('/customers/{customer}/invoices/create', [CustomerInvoiceController::class, 'create'])->middleware('permission:create invoices')->name('customers.invoices.create');
    Route::post('/customers/{customer}/invoices', [CustomerInvoiceController::class, 'store'])->middleware('permission:create invoices')->name('customers.invoices.store');
});
