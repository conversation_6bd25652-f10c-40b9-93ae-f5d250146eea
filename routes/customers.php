<?php

use App\Http\Controllers\Web\CustomerController;
use App\Http\Controllers\Web\CustomerInvoiceController;
use App\Http\Controllers\Web\CustomerSubscriptionController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified'])->group(function () {
    // Customer routes
    Route::get('/customers', [CustomerController::class, 'index'])->name('customers.index');
    Route::get('/customers/create', [CustomerController::class, 'create'])->name('customers.create');
    Route::post('/customers', [CustomerController::class, 'store'])->name('customers.store');
    Route::get('/customers/{customer}', [CustomerController::class, 'show'])->name('customers.show');
    Route::get('/customers/{customer}/edit', [CustomerController::class, 'edit'])->name('customers.edit');
    Route::put('/customers/{customer}', [CustomerController::class, 'update'])->name('customers.update');
    Route::delete('/customers/{customer}', [CustomerController::class, 'destroy'])->name('customers.destroy');
    Route::post('/customers/{customer}/generate-mpesa-id', [CustomerController::class, 'generateMpesaId'])->name('customers.generate-mpesa-id');

    // Customer subscriptions routes
    Route::get('/customers/{customer}/subscriptions', [CustomerSubscriptionController::class, 'index'])->name('customers.subscriptions.index');
    Route::get('/customers/{customer}/subscriptions/create', [CustomerSubscriptionController::class, 'create'])->name('customers.subscriptions.create');
    Route::post('/customers/{customer}/subscriptions', [CustomerSubscriptionController::class, 'store'])->name('customers.subscriptions.store');

    // Customer invoices routes
    Route::get('/customers/{customer}/invoices', [CustomerInvoiceController::class, 'index'])->name('customers.invoices.index');
    Route::get('/customers/{customer}/invoices/create', [CustomerInvoiceController::class, 'create'])->name('customers.invoices.create');
    Route::post('/customers/{customer}/invoices', [CustomerInvoiceController::class, 'store'])->name('customers.invoices.store');
});
