<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::middleware(['auth', 'verified'])->group(function () {
    // Main Network Dashboard
    Route::get('network', [\App\Http\Controllers\Network\NetworkController::class, 'index'])->name('network.index');

    // Network Sites
    Route::get('network/sites', [\App\Http\Controllers\Network\NetworkSiteController::class, 'index'])->name('network.sites.index');
    Route::get('network/sites/create', [\App\Http\Controllers\Network\NetworkSiteController::class, 'create'])->name('network.sites.create');
    Route::post('network/sites', [\App\Http\Controllers\Network\NetworkSiteController::class, 'store'])->name('network.sites.store');
    Route::get('network/sites/{site}', [\App\Http\Controllers\Network\NetworkSiteController::class, 'show'])->name('network.sites.show');
    Route::get('network/sites/{site}/edit', [\App\Http\Controllers\Network\NetworkSiteController::class, 'edit'])->name('network.sites.edit');
    Route::put('network/sites/{site}', [\App\Http\Controllers\Network\NetworkSiteController::class, 'update'])->name('network.sites.update');
    Route::delete('network/sites/{site}', [\App\Http\Controllers\Network\NetworkSiteController::class, 'destroy'])->name('network.sites.destroy');

    // Network Devices
    Route::get('network/devices', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'index'])->name('network.devices.index');

    Route::get('network/devices/create', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'create'])->name('network.devices.create');

    Route::get('network/devices/{device}', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'show'])->name('network.devices.show');

    Route::get('network/devices/{device}/edit', function ($device) {
        return Inertia::render('network/devices/edit', [
            'deviceId' => $device,
        ]);
    })->name('network.devices.edit');

    Route::get('network/devices/{device}/interfaces', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'interfaces'])->name('network.devices.interfaces');

    // AJAX route for real-time interface updates
    Route::get('network/api/devices/{device}/interfaces', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'interfacesApi'])->name('network.devices.interfaces.api');

    // Live MikroTik interface fetch
    Route::get('network/devices/{device}/interfaces/{interface}/live', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'interfaceFromDevice'])->name('network.devices.interfaces.live');

    // Network Device API Routes
    Route::post('network/devices', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'store'])->name('network.devices.store');
    Route::put('network/devices/{device}', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'update'])->name('network.devices.update');
    Route::delete('network/devices/{device}', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'destroy'])->name('network.devices.destroy');
    Route::post('network/devices/{device}/test-connection', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'testConnection'])->name('network.devices.test-connection');
    Route::get('network/api/interfaces/{interface}', [\App\Http\Controllers\Network\NetworkInterfaceController::class, 'show'])->name('network.interfaces.show.api');

    // Network Interfaces
    Route::get('network/interfaces', function () {
        return Inertia::render('network/interfaces/index');
    })->name('network.interfaces.index');

    Route::get('network/interfaces/create', function () {
        return Inertia::render('network/interfaces/create');
    })->name('network.interfaces.create');

    Route::get('network/interfaces/{interface}', function ($interface) {
        return Inertia::render('network/interfaces/show', [
            'interfaceId' => $interface,
        ]);
    })->name('network.interfaces.show');

    Route::get('network/interfaces/{interface}/edit', function ($interface) {
        return Inertia::render('network/interfaces/edit', [
            'interfaceId' => $interface,
        ]);
    })->name('network.interfaces.edit');

    // Network Connections
    Route::get('network/connections', function () {
        return Inertia::render('network/connections/index');
    })->name('network.connections.index');

    Route::get('network/connections/create', function () {
        return Inertia::render('network/connections/create');
    })->name('network.connections.create');

    Route::get('network/connections/{connection}', function ($connection) {
        return Inertia::render('network/connections/show', [
            'connectionId' => $connection,
        ]);
    })->name('network.connections.show');

    Route::get('network/connections/{connection}/edit', function ($connection) {
        return Inertia::render('network/connections/edit', [
            'connectionId' => $connection,
        ]);
    })->name('network.connections.edit');

    // Network Maps
    Route::get('network/maps', function () {
        return Inertia::render('network/maps/index');
    })->name('network.maps.index');

    Route::get('network/maps/create', function () {
        return Inertia::render('network/maps/create');
    })->name('network.maps.create');

    Route::get('network/maps/{map}', function ($map) {
        return Inertia::render('network/maps/show', [
            'mapId' => $map,
        ]);
    })->name('network.maps.show');

    Route::get('network/maps/{map}/edit', function ($map) {
        return Inertia::render('network/maps/edit', [
            'mapId' => $map,
        ]);
    })->name('network.maps.edit');
});
