<?php

use App\Http\Controllers\Settings\PasswordController;
use App\Http\Controllers\Settings\ProfileController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::middleware('auth')->group(function () {
    Route::redirect('settings', 'settings/profile');

    Route::get('settings/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('settings/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('settings/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::get('settings/password', [PasswordController::class, 'edit'])->name('password.edit');
    Route::put('settings/password', [PasswordController::class, 'update'])->name('password.update');

    Route::get('settings/appearance', function () {
        return Inertia::render('settings/appearance');
    })->name('appearance');
});

// Admin Settings routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::prefix('admin/settings')->name('admin.settings.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('index');
        Route::get('/mpesa-id', [\App\Http\Controllers\Admin\SettingsController::class, 'mpesaId'])->name('mpesa-id');
        Route::put('/mpesa-id', [\App\Http\Controllers\Admin\SettingsController::class, 'updateMpesaId'])->name('mpesa-id.update');
        Route::post('/mpesa-id/bulk-generate', [\App\Http\Controllers\Admin\SettingsController::class, 'bulkGenerateMpesaIds'])->name('mpesa-id.bulk-generate');
        Route::post('/mpesa-id/preview', [\App\Http\Controllers\Admin\SettingsController::class, 'previewMpesaId'])->name('mpesa-id.preview');

        // User Management routes
        Route::resource('users', \App\Http\Controllers\Admin\UserController::class);

        // Role Management routes
        Route::resource('roles', \App\Http\Controllers\Admin\RoleController::class);
    });

    // Import Template & Documentation routes
    Route::prefix('admin/import')->name('admin.import.')->group(function () {
        Route::get('/documentation', [\App\Http\Controllers\Web\ImportTemplateController::class, 'documentation'])->name('documentation');
        Route::get('/template/download', [\App\Http\Controllers\Web\ImportTemplateController::class, 'downloadTemplate'])->name('template.download');
        Route::get('/template/download-csv', [\App\Http\Controllers\Web\ImportTemplateController::class, 'downloadCsvTemplate'])->name('template.download-csv');
        Route::get('/upload', [\App\Http\Controllers\Web\ImportTemplateController::class, 'showImportForm'])->name('upload');
        Route::post('/process', [\App\Http\Controllers\Web\ImportTemplateController::class, 'processImport'])->name('process');
    });
});
