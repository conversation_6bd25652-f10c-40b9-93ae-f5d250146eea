<?php

use App\Http\Controllers\Settings\PasswordController;
use App\Http\Controllers\Settings\ProfileController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::middleware('auth')->group(function () {
    Route::redirect('settings', 'settings/profile');

    Route::get('settings/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('settings/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('settings/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::get('settings/password', [PasswordController::class, 'edit'])->name('password.edit');
    Route::put('settings/password', [PasswordController::class, 'update'])->name('password.update');

    Route::get('settings/appearance', function () {
        return Inertia::render('settings/appearance');
    })->name('appearance');
});

// Admin Settings routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::prefix('admin/settings')->name('admin.settings.')->middleware('permission:view admin settings')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('index');
        Route::get('/mpesa-id', [\App\Http\Controllers\Admin\SettingsController::class, 'mpesaId'])->name('mpesa-id');
        Route::put('/mpesa-id', [\App\Http\Controllers\Admin\SettingsController::class, 'updateMpesaId'])->name('mpesa-id.update');
        Route::post('/mpesa-id/bulk-generate', [\App\Http\Controllers\Admin\SettingsController::class, 'bulkGenerateMpesaIds'])->name('mpesa-id.bulk-generate');
        Route::post('/mpesa-id/preview', [\App\Http\Controllers\Admin\SettingsController::class, 'previewMpesaId'])->name('mpesa-id.preview');

        // User Management routes
        Route::get('users', [\App\Http\Controllers\Admin\UserController::class, 'index'])
            ->middleware('permission:view users')->name('users.index');
        Route::get('users/create', [\App\Http\Controllers\Admin\UserController::class, 'create'])
            ->middleware('permission:create users')->name('users.create');
        Route::post('users', [\App\Http\Controllers\Admin\UserController::class, 'store'])
            ->middleware('permission:create users')->name('users.store');
        Route::get('users/{user}', [\App\Http\Controllers\Admin\UserController::class, 'show'])
            ->middleware('permission:view users')->name('users.show');
        Route::get('users/{user}/edit', [\App\Http\Controllers\Admin\UserController::class, 'edit'])
            ->middleware('permission:edit users')->name('users.edit');
        Route::put('users/{user}', [\App\Http\Controllers\Admin\UserController::class, 'update'])
            ->middleware('permission:edit users')->name('users.update');
        Route::delete('users/{user}', [\App\Http\Controllers\Admin\UserController::class, 'destroy'])
            ->middleware('permission:delete users')->name('users.destroy');

        // Role Management routes
        Route::get('roles', [\App\Http\Controllers\Admin\RoleController::class, 'index'])
            ->middleware('permission:view roles')->name('roles.index');
        Route::get('roles/create', [\App\Http\Controllers\Admin\RoleController::class, 'create'])
            ->middleware('permission:create roles')->name('roles.create');
        Route::post('roles', [\App\Http\Controllers\Admin\RoleController::class, 'store'])
            ->middleware('permission:create roles')->name('roles.store');
        Route::get('roles/{role}', [\App\Http\Controllers\Admin\RoleController::class, 'show'])
            ->middleware('permission:view roles')->name('roles.show');
        Route::get('roles/{role}/edit', [\App\Http\Controllers\Admin\RoleController::class, 'edit'])
            ->middleware('permission:edit roles')->name('roles.edit');
        Route::put('roles/{role}', [\App\Http\Controllers\Admin\RoleController::class, 'update'])
            ->middleware('permission:edit roles')->name('roles.update');
        Route::delete('roles/{role}', [\App\Http\Controllers\Admin\RoleController::class, 'destroy'])
            ->middleware('permission:delete roles')->name('roles.destroy');
    });

    // Import Template & Documentation routes
    Route::prefix('admin/import')->name('admin.import.')->group(function () {
        Route::get('/documentation', [\App\Http\Controllers\Web\ImportTemplateController::class, 'documentation'])->name('documentation');
        Route::get('/template/download', [\App\Http\Controllers\Web\ImportTemplateController::class, 'downloadTemplate'])->name('template.download');
        Route::get('/template/download-csv', [\App\Http\Controllers\Web\ImportTemplateController::class, 'downloadCsvTemplate'])->name('template.download-csv');
        Route::get('/upload', [\App\Http\Controllers\Web\ImportTemplateController::class, 'showImportForm'])->name('upload');
        Route::post('/process', [\App\Http\Controllers\Web\ImportTemplateController::class, 'processImport'])->name('process');
    });
});
