<?php

namespace App\Services;

use App\Models\SystemSetting;
use Illuminate\Support\Facades\Cache;

class CurrencyService
{
    /**
     * Supported currencies with their symbols and formatting rules
     */
    const SUPPORTED_CURRENCIES = [
        'USD' => [
            'name' => 'US Dollar',
            'symbol' => '$',
            'code' => 'USD',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'symbol_position' => 'before',
        ],
        'KES' => [
            'name' => 'Kenyan Shilling',
            'symbol' => 'KSh',
            'code' => 'KES',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'symbol_position' => 'before',
        ],
        'ETB' => [
            'name' => 'Ethiopian Birr',
            'symbol' => 'Br',
            'code' => 'ETB',
            'decimal_places' => 2,
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'symbol_position' => 'after',
        ],
        'UGX' => [
            'name' => 'Ugandan Shilling',
            'symbol' => 'USh',
            'code' => 'UGX',
            'decimal_places' => 0, // UGX typically doesn't use decimal places
            'thousands_separator' => ',',
            'decimal_separator' => '.',
            'symbol_position' => 'before',
        ],
    ];

    /**
     * Get the default currency from system settings
     */
    public static function getDefaultCurrency(): string
    {
        return Cache::remember('default_currency', 3600, function () {
            $setting = SystemSetting::where('key', 'default_currency')->first();
            return $setting ? $setting->value : 'KES';
        });
    }

    /**
     * Get currency display format from system settings
     */
    public static function getDisplayFormat(): string
    {
        return Cache::remember('currency_display_format', 3600, function () {
            $setting = SystemSetting::where('key', 'currency_display_format')->first();
            return $setting ? $setting->value : 'symbol_before';
        });
    }

    /**
     * Get decimal places from system settings
     */
    public static function getDecimalPlaces(): int
    {
        return Cache::remember('currency_decimal_places', 3600, function () {
            $setting = SystemSetting::where('key', 'currency_decimal_places')->first();
            return $setting ? (int) $setting->value : 2;
        });
    }

    /**
     * Get currency information
     */
    public static function getCurrencyInfo(?string $currency = null): array
    {
        $currency = $currency ?: self::getDefaultCurrency();
        return self::SUPPORTED_CURRENCIES[$currency] ?? self::SUPPORTED_CURRENCIES['KES'];
    }

    /**
     * Format amount with currency
     */
    public static function format(float $amount, ?string $currency = null, array $options = []): string
    {
        $currency = $currency ?: self::getDefaultCurrency();
        $currencyInfo = self::getCurrencyInfo($currency);

        // Override with system settings or options
        $decimalPlaces = $options['decimal_places'] ?? self::getDecimalPlaces();
        $displayFormat = $options['display_format'] ?? self::getDisplayFormat();
        $thousandsSeparator = $options['thousands_separator'] ?? $currencyInfo['thousands_separator'];
        $decimalSeparator = $options['decimal_separator'] ?? $currencyInfo['decimal_separator'];

        // Use currency-specific decimal places if not overridden
        if (!isset($options['decimal_places']) && isset($currencyInfo['decimal_places'])) {
            $decimalPlaces = $currencyInfo['decimal_places'];
        }

        // Format the number
        $formattedAmount = number_format($amount, $decimalPlaces, $decimalSeparator, $thousandsSeparator);

        // Apply display format
        switch ($displayFormat) {
            case 'symbol_before':
                return $currencyInfo['symbol'] . ' ' . $formattedAmount;
            case 'symbol_after':
                return $formattedAmount . ' ' . $currencyInfo['symbol'];
            case 'code_before':
                return $currencyInfo['code'] . ' ' . $formattedAmount;
            case 'code_after':
                return $formattedAmount . ' ' . $currencyInfo['code'];
            default:
                return $currencyInfo['symbol'] . ' ' . $formattedAmount;
        }
    }

    /**
     * Format amount for display in tables/lists (compact format)
     */
    public static function formatCompact(float $amount, ?string $currency = null): string
    {
        $currency = $currency ?: self::getDefaultCurrency();
        $currencyInfo = self::getCurrencyInfo($currency);

        $decimalPlaces = $currencyInfo['decimal_places'];
        $formattedAmount = number_format($amount, $decimalPlaces, '.', ',');

        return $currencyInfo['symbol'] . $formattedAmount;
    }

    /**
     * Get all supported currencies for dropdowns
     */
    public static function getSupportedCurrencies(): array
    {
        return array_map(function ($currency, $code) {
            return [
                'code' => $code,
                'name' => $currency['name'],
                'symbol' => $currency['symbol'],
                'display' => "{$currency['name']} ({$currency['symbol']})",
            ];
        }, self::SUPPORTED_CURRENCIES, array_keys(self::SUPPORTED_CURRENCIES));
    }

    /**
     * Get currency symbol
     */
    public static function getSymbol(?string $currency = null): string
    {
        $currency = $currency ?: self::getDefaultCurrency();
        $currencyInfo = self::getCurrencyInfo($currency);
        return $currencyInfo['symbol'];
    }

    /**
     * Get currency code
     */
    public static function getCode(?string $currency = null): string
    {
        $currency = $currency ?: self::getDefaultCurrency();
        return $currency;
    }

    /**
     * Validate currency code
     */
    public static function isValidCurrency(string $currency): bool
    {
        return array_key_exists($currency, self::SUPPORTED_CURRENCIES);
    }

    /**
     * Clear currency cache (call when settings are updated)
     */
    public static function clearCache(): void
    {
        Cache::forget('default_currency');
        Cache::forget('currency_display_format');
        Cache::forget('currency_decimal_places');
    }

    /**
     * Convert amount to default currency (placeholder for future currency conversion)
     */
    public static function convertToDefault(float $amount, string $fromCurrency): float
    {
        // For now, we assume all amounts are stored in the same currency
        // This method is a placeholder for future currency conversion functionality
        return $amount;
    }

    /**
     * Get currency for JavaScript/frontend
     */
    public static function getJavaScriptConfig(): array
    {
        $defaultCurrency = self::getDefaultCurrency();
        $currencyInfo = self::getCurrencyInfo($defaultCurrency);

        return [
            'default_currency' => $defaultCurrency,
            'currency_info' => $currencyInfo,
            'display_format' => self::getDisplayFormat(),
            'decimal_places' => self::getDecimalPlaces(),
            'supported_currencies' => self::getSupportedCurrencies(),
        ];
    }
}
