<?php

namespace App\Observers;

use App\Models\Customer;
use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use Illuminate\Support\Facades\Log;

class CustomerObserver
{
    /**
     * Handle the Customer "updated" event.
     */
    public function updated(Customer $customer): void
    {
        // Check if the status was changed
        if ($customer->isDirty('status')) {
            $oldStatus = $customer->getOriginal('status');
            $newStatus = $customer->status;

            Log::info('Customer status changed', [
                'customer_id' => $customer->id,
                'customer_name' => $customer->name,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
            ]);

            // Handle status changes
            if ($newStatus === 'inactive' || $newStatus === 'suspended') {
                $this->suspendCustomerServices($customer);
            } elseif ($newStatus === 'active' && ($oldStatus === 'inactive' || $oldStatus === 'suspended')) {
                $this->activateCustomerServices($customer);
            }
        }
    }

    /**
     * Suspend all services for a customer.
     */
    protected function suspendCustomerServices(Customer $customer): void
    {
        Log::info('Suspending all services for customer', [
            'customer_id' => $customer->id,
            'customer_name' => $customer->name,
            'customer_status' => $customer->status,
        ]);

        // Suspend all Static IP services
        $staticIpServices = StaticIpService::where('customer_id', $customer->id)
            ->where('status', 'active')
            ->get();

        foreach ($staticIpServices as $service) {
            Log::info('Suspending Static IP service due to customer status', [
                'customer_id' => $customer->id,
                'service_id' => $service->id,
                'ip_address' => $service->ip_address,
            ]);

            try {
                // Only suspend if service is active
                if ($service->status === 'active') {
                    // Update MikroTik configuration first
                    $controller = new \App\Http\Controllers\Services\StaticIpServiceController;
                    $reflection = new \ReflectionClass($controller);
                    $method = $reflection->getMethod('suspendStaticIpOnMikrotik');
                    $method->setAccessible(true);
                    $success = $method->invoke($controller, $service);

                    if ($success) {
                        Log::info('Static IP service suspended successfully', [
                            'customer_id' => $customer->id,
                            'service_id' => $service->id,
                        ]);
                    } else {
                        Log::warning('Failed to suspend Static IP service on MikroTik', [
                            'customer_id' => $customer->id,
                            'service_id' => $service->id,
                        ]);
                    }
                } else {
                    Log::info('Static IP service already suspended', [
                        'customer_id' => $customer->id,
                        'service_id' => $service->id,
                        'current_status' => $service->status,
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Failed to suspend Static IP service', [
                    'customer_id' => $customer->id,
                    'service_id' => $service->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // Suspend all PPPoE services
        $pppoeServices = PppoeService::where('customer_id', $customer->id)
            ->where('status', 'active')
            ->get();

        foreach ($pppoeServices as $service) {
            Log::info('Suspending PPPoE service due to customer status', [
                'customer_id' => $customer->id,
                'service_id' => $service->id,
                'username' => $service->username,
            ]);

            try {
                // Update service status
                $service->status = 'suspended';
                $service->save();

                // Update MikroTik configuration
                $controller = new \App\Http\Controllers\Services\PppoeServiceController;
                $reflection = new \ReflectionClass($controller);
                $method = $reflection->getMethod('suspendPppoeOnMikrotik');
                $method->setAccessible(true);
                $method->invoke($controller, $service);

                Log::info('PPPoE service suspended successfully', [
                    'customer_id' => $customer->id,
                    'service_id' => $service->id,
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to suspend PPPoE service', [
                    'customer_id' => $customer->id,
                    'service_id' => $service->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        Log::info('Customer service suspension completed', [
            'customer_id' => $customer->id,
            'static_ip_services' => $staticIpServices->count(),
            'pppoe_services' => $pppoeServices->count(),
        ]);
    }

    /**
     * Activate all services for a customer.
     */
    protected function activateCustomerServices(Customer $customer): void
    {
        Log::info('Activating all services for customer', [
            'customer_id' => $customer->id,
            'customer_name' => $customer->name,
            'customer_status' => $customer->status,
        ]);

        // Activate all Static IP services
        $staticIpServices = StaticIpService::where('customer_id', $customer->id)
            ->where('status', 'suspended')
            ->get();

        foreach ($staticIpServices as $service) {
            Log::info('Activating Static IP service due to customer status', [
                'customer_id' => $customer->id,
                'service_id' => $service->id,
                'ip_address' => $service->ip_address,
            ]);

            try {
                // Update service status
                $service->status = 'active';
                $service->save();

                // Update MikroTik configuration
                $controller = new \App\Http\Controllers\Services\StaticIpServiceController;
                $reflection = new \ReflectionClass($controller);
                $method = $reflection->getMethod('activateStaticIpOnMikrotik');
                $method->setAccessible(true);
                $method->invoke($controller, $service);

                Log::info('Static IP service activated successfully', [
                    'customer_id' => $customer->id,
                    'service_id' => $service->id,
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to activate Static IP service', [
                    'customer_id' => $customer->id,
                    'service_id' => $service->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // Activate all PPPoE services
        $pppoeServices = PppoeService::where('customer_id', $customer->id)
            ->where('status', 'suspended')
            ->get();

        foreach ($pppoeServices as $service) {
            Log::info('Activating PPPoE service due to customer status', [
                'customer_id' => $customer->id,
                'service_id' => $service->id,
                'username' => $service->username,
            ]);

            try {
                // Update service status
                $service->status = 'active';
                $service->save();

                // Update MikroTik configuration
                $controller = new \App\Http\Controllers\Services\PppoeServiceController;
                $reflection = new \ReflectionClass($controller);
                $method = $reflection->getMethod('activatePppoeOnMikrotik');
                $method->setAccessible(true);
                $method->invoke($controller, $service);

                Log::info('PPPoE service activated successfully', [
                    'customer_id' => $customer->id,
                    'service_id' => $service->id,
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to activate PPPoE service', [
                    'customer_id' => $customer->id,
                    'service_id' => $service->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        Log::info('Customer service activation completed', [
            'customer_id' => $customer->id,
            'static_ip_services' => $staticIpServices->count(),
            'pppoe_services' => $pppoeServices->count(),
        ]);
    }
}
