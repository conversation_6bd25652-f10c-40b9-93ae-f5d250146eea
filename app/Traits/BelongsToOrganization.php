<?php

namespace App\Traits;

use App\Models\Organization;
use App\Services\OrganizationContext;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait BelongsToOrganization
{
    /**
     * Boot the trait.
     */
    protected static function bootBelongsToOrganization()
    {
        // Automatically scope all queries to the current organization
        static::addGlobalScope('organization', function (Builder $builder) {
            $organizationContext = app(OrganizationContext::class);
            
            if ($organizationContext->hasOrganization()) {
                $builder->where('organization_id', $organizationContext->getId());
            }
        });

        // Automatically set organization_id when creating new records
        static::creating(function ($model) {
            if (empty($model->organization_id)) {
                $organizationContext = app(OrganizationContext::class);
                
                if ($organizationContext->hasOrganization()) {
                    $model->organization_id = $organizationContext->getId();
                }
            }
        });

        // Validate organization ownership on update/delete
        static::updating(function ($model) {
            static::validateOrganizationOwnership($model);
        });

        static::deleting(function ($model) {
            static::validateOrganizationOwnership($model);
        });
    }

    /**
     * Get the organization that owns the model.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Scope a query to a specific organization.
     */
    public function scopeForOrganization(Builder $query, $organizationId): Builder
    {
        return $query->where('organization_id', $organizationId);
    }

    /**
     * Scope a query without organization filtering.
     * Use with caution - only for admin/system operations.
     */
    public function scopeWithoutOrganizationScope(Builder $query): Builder
    {
        return $query->withoutGlobalScope('organization');
    }

    /**
     * Check if the model belongs to the current organization.
     */
    public function belongsToCurrentOrganization(): bool
    {
        $organizationContext = app(OrganizationContext::class);
        
        return $organizationContext->hasOrganization() && 
               $this->organization_id === $organizationContext->getId();
    }

    /**
     * Check if the model belongs to a specific organization.
     */
    public function belongsToOrganization($organizationId): bool
    {
        return $this->organization_id == $organizationId;
    }

    /**
     * Validate that the model belongs to the current organization.
     * Throws exception if not.
     */
    protected static function validateOrganizationOwnership($model)
    {
        if (!$model->belongsToCurrentOrganization()) {
            throw new \Exception('Access denied: Record does not belong to your organization.');
        }
    }

    /**
     * Get the organization ID for the model.
     */
    public function getOrganizationId()
    {
        return $this->organization_id;
    }

    /**
     * Set the organization ID for the model.
     */
    public function setOrganizationId($organizationId)
    {
        $this->organization_id = $organizationId;
        return $this;
    }
}
