<?php

namespace App\Models\Services;

use App\Models\Bandwidth\BandwidthPlan;
use App\Models\Customer;
use App\Models\Network\NetworkDevice;
use App\Models\Subscription;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StaticIpService extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'subscription_id',
        'customer_id',
        'device_id',
        'ip_address',
        'subnet_mask',
        'gateway',
        'dns_servers',
        'bandwidth_plan_id',
        'ip_pool_id',
        'status',
        'provisioning_status',
        'provisioning_stage',
        'provisioning_progress',
        'provisioning_error',
        'provisioning_started_at',
        'provisioning_completed_at',
        'last_provisioning_attempt',
        'provisioning_attempts',
        'firewall_rules',
        'nat_rules',
        'comment',
        'mikrotik_route_id',
        'mikrotik_firewall_id',
        'mikrotik_nat_id',
        'mikrotik_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'dns_servers' => 'array',
        'firewall_rules' => 'array',
        'nat_rules' => 'array',
        'provisioning_started_at' => 'datetime',
        'provisioning_completed_at' => 'datetime',
        'last_provisioning_attempt' => 'datetime',
        'provisioning_progress' => 'integer',
        'provisioning_attempts' => 'integer',
    ];

    /**
     * Get the customer that owns the Static IP service.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the subscription associated with the Static IP service.
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Get the device (router) that hosts this Static IP service.
     */
    public function device(): BelongsTo
    {
        return $this->belongsTo(NetworkDevice::class, 'device_id');
    }

    /**
     * Get the bandwidth plan associated with the Static IP service.
     */
    public function bandwidthPlan(): BelongsTo
    {
        return $this->belongsTo(BandwidthPlan::class, 'bandwidth_plan_id');
    }

    /**
     * Get the IP pool this service's IP was allocated from.
     */
    public function ipPool(): BelongsTo
    {
        return $this->belongsTo(IpPool::class, 'ip_pool_id');
    }

    /**
     * Scope a query to only include active Static IP services.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Check if the service is currently being provisioned.
     */
    public function isProvisioning(): bool
    {
        return $this->provisioning_status === 'provisioning';
    }

    /**
     * Check if provisioning has failed.
     */
    public function hasProvisioningFailed(): bool
    {
        return in_array($this->provisioning_status, ['provisioning_failed', 'reactivation_failed']);
    }

    /**
     * Check if the service is fully provisioned and active.
     */
    public function isFullyProvisioned(): bool
    {
        return $this->provisioning_status === 'active' && $this->status === 'active';
    }

    /**
     * Start provisioning process.
     */
    public function startProvisioning(string $stage = 'Initializing provisioning...'): void
    {
        $this->update([
            'provisioning_status' => 'provisioning',
            'provisioning_stage' => $stage,
            'provisioning_progress' => 0,
            'provisioning_error' => null,
            'provisioning_started_at' => now(),
            'provisioning_completed_at' => null,
            'last_provisioning_attempt' => now(),
            'provisioning_attempts' => $this->provisioning_attempts + 1,
        ]);
    }

    /**
     * Update provisioning progress.
     */
    public function updateProvisioningProgress(string $stage, int $progress): void
    {
        $this->update([
            'provisioning_stage' => $stage,
            'provisioning_progress' => min(100, max(0, $progress)),
        ]);
    }

    /**
     * Mark provisioning as completed successfully.
     */
    public function completeProvisioning(): void
    {
        $this->update([
            'provisioning_status' => 'active',
            'provisioning_stage' => 'Provisioning completed successfully',
            'provisioning_progress' => 100,
            'provisioning_error' => null,
            'provisioning_completed_at' => now(),
            'status' => 'active', // Also update the main status
        ]);
    }

    /**
     * Mark provisioning as failed.
     */
    public function failProvisioning(string $error, bool $isReactivation = false): void
    {
        $this->update([
            'provisioning_status' => $isReactivation ? 'reactivation_failed' : 'provisioning_failed',
            'provisioning_stage' => 'Provisioning failed',
            'provisioning_error' => $error,
            'provisioning_completed_at' => null,
        ]);
    }

    /**
     * Check if the service can be re-provisioned (rate limiting).
     */
    public function canBeReprovisioned(): bool
    {
        // Allow re-provisioning if:
        // 1. Not currently provisioning
        // 2. Last attempt was more than 5 minutes ago OR less than 3 attempts in last hour
        if ($this->isProvisioning()) {
            return false;
        }

        if (!$this->last_provisioning_attempt) {
            return true;
        }

        $fiveMinutesAgo = now()->subMinutes(5);
        $oneHourAgo = now()->subHour();

        // If last attempt was more than 5 minutes ago, allow
        if ($this->last_provisioning_attempt->lt($fiveMinutesAgo)) {
            return true;
        }

        // If less than 3 attempts in the last hour, allow
        $recentAttempts = static::where('id', $this->id)
            ->where('last_provisioning_attempt', '>=', $oneHourAgo)
            ->value('provisioning_attempts') ?? 0;

        return $recentAttempts < 3;
    }

    /**
     * Scope a query to only include suspended Static IP services.
     */
    public function scopeSuspended($query)
    {
        return $query->where('status', 'suspended');
    }

    /**
     * Scope a query to only include pending Static IP services.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include services for a specific customer.
     */
    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * Scope a query to only include services on a specific device.
     */
    public function scopeOnDevice($query, $deviceId)
    {
        return $query->where('device_id', $deviceId);
    }

    /**
     * Check if the service is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if the service is suspended.
     */
    public function isSuspended(): bool
    {
        return $this->status === 'suspended';
    }

    /**
     * Activate the Static IP service.
     */
    public function activate(): bool
    {
        if ($this->status !== 'active') {
            $this->status = 'active';

            return $this->save();
        }

        return true;
    }

    /**
     * Suspend the Static IP service.
     */
    public function suspend(): bool
    {
        if ($this->status !== 'suspended') {
            $this->status = 'suspended';

            return $this->save();
        }

        return true;
    }

    /**
     * Get the CIDR notation for this IP address and subnet.
     */
    public function getCidrAttribute(): string
    {
        // Convert subnet mask to CIDR prefix
        $subnet_bits = 0;
        $subnet_octets = explode('.', $this->subnet_mask);

        foreach ($subnet_octets as $octet) {
            $subnet_bits += substr_count(decbin((int) $octet), '1');
        }

        return $this->ip_address.'/'.$subnet_bits;
    }

    /**
     * Get the network address for this IP and subnet.
     */
    public function getNetworkAddressAttribute(): string
    {
        $ip_octets = explode('.', $this->ip_address);
        $subnet_octets = explode('.', $this->subnet_mask);
        $network_octets = [];

        for ($i = 0; $i < 4; $i++) {
            $network_octets[] = (int) $ip_octets[$i] & (int) $subnet_octets[$i];
        }

        return implode('.', $network_octets);
    }
}
