<?php

namespace App\Http\Controllers\Bandwidth;

use App\Http\Controllers\Controller;
use App\Models\Bandwidth\BandwidthAssignment;
use App\Models\Bandwidth\BandwidthPlan;
use App\Services\CurrencyService;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class BandwidthPlanController extends Controller
{
    /**
     * Display a listing of the plans.
     */
    public function index(Request $request)
    {
        $query = BandwidthPlan::query();

        // Apply filters
        if ($request->has('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%'.$request->search.'%')
                    ->orWhere('description', 'like', '%'.$request->search.'%');
            });
        }

        if ($request->has('active')) {
            $query->where('active', $request->boolean('active'));
        }

        if ($request->has('min_download_speed')) {
            $query->where('download_speed', '>=', $request->min_download_speed);
        }

        if ($request->has('min_upload_speed')) {
            $query->where('upload_speed', '>=', $request->min_upload_speed);
        }

        if ($request->has('priority')) {
            $query->where('priority', $request->priority);
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);

        $plans = $query->paginate($request->get('per_page', 15));

        // Check if this is an API request or web request
        if ($request->expectsJson()) {
            return response()->json($plans);
        }

        // For Inertia/web requests, return Inertia response
        return Inertia::render('bandwidth/plans/index', [
            'plans' => $plans,
            'filters' => $request->only(['search', 'active', 'sort_field', 'sort_direction', 'per_page']),
        ]);
    }

    /**
     * Store a newly created plan.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:bandwidth_plans',
            'description' => 'nullable|string',
            'download_speed' => 'required|integer|min:1',
            'upload_speed' => 'required|integer|min:1',
            'burst_download_speed' => 'nullable|integer|min:1',
            'burst_upload_speed' => 'nullable|integer|min:1',
            'burst_time' => 'nullable|integer|min:1',
            'priority' => 'integer|min:1|max:8',
            'price' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|in:USD,KES,ETB,UGX',
            'active' => 'boolean',
        ]);

        // Set default currency if not provided
        if (!isset($validated['currency'])) {
            $validated['currency'] = CurrencyService::getDefaultCurrency();
        }

        $plan = BandwidthPlan::create($validated);

        // Check if this is an API request or web request
        if ($request->expectsJson()) {
            return response()->json($plan, 201);
        }

        // For Inertia/web requests, redirect with success message
        return redirect()->route('bandwidth.plans.index')
            ->with('success', 'Bandwidth plan created successfully.');
    }

    /**
     * Display the specified plan.
     */
    public function show(BandwidthPlan $plan)
    {
        return response()->json($plan);
    }

    /**
     * Update the specified plan.
     */
    public function update(Request $request, BandwidthPlan $plan)
    {
        $validated = $request->validate([
            'name' => [
                'string',
                'max:255',
                Rule::unique('bandwidth_plans')->ignore($plan->id),
            ],
            'description' => 'nullable|string',
            'download_speed' => 'integer|min:1',
            'upload_speed' => 'integer|min:1',
            'burst_download_speed' => 'nullable|integer|min:1',
            'burst_upload_speed' => 'nullable|integer|min:1',
            'burst_time' => 'nullable|integer|min:1',
            'priority' => 'integer|min:1|max:8',
            'price' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|in:USD,KES,ETB,UGX',
            'active' => 'boolean',
        ]);

        $plan->update($validated);

        return response()->json($plan);
    }

    /**
     * Remove the specified plan.
     */
    public function destroy(BandwidthPlan $plan)
    {
        // Check if the plan has assignments
        $assignmentsCount = $plan->assignments()->count();
        if ($assignmentsCount > 0) {
            return response()->json([
                'message' => 'Cannot delete plan with assignments. There are '.$assignmentsCount.' assignments using this plan.',
            ], 422);
        }

        $plan->delete();

        return response()->json(null, 204);
    }

    /**
     * Get the assignments for the specified plan.
     */
    public function assignments(BandwidthPlan $plan, Request $request)
    {
        $query = $plan->assignments();

        // Apply filters
        if ($request->has('assignee_type')) {
            $query->where('assignee_type', $request->assignee_type);
        }

        if ($request->has('active')) {
            $query->where('active', $request->boolean('active'));
        }

        if ($request->has('current')) {
            $query->inEffect();
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $assignments = $query->with('assignee')->paginate($request->get('per_page', 15));

        return response()->json($assignments);
    }

    /**
     * Assign the plan to a model.
     */
    public function assign(Request $request, BandwidthPlan $plan)
    {
        $validated = $request->validate([
            'assignee_type' => 'required|string',
            'assignee_id' => 'required|integer',
            'starts_at' => 'nullable|date',
            'ends_at' => 'nullable|date|after:starts_at',
            'active' => 'boolean',
        ]);

        // Check if the assignee exists
        $assigneeClass = $validated['assignee_type'];
        $assigneeId = $validated['assignee_id'];

        if (! class_exists($assigneeClass)) {
            return response()->json([
                'message' => 'Invalid assignee type',
            ], 422);
        }

        $assignee = $assigneeClass::find($assigneeId);
        if (! $assignee) {
            return response()->json([
                'message' => 'Assignee not found',
            ], 404);
        }

        // Check if an assignment already exists
        $existingAssignment = BandwidthAssignment::where('assignable_type', get_class($plan))
            ->where('assignable_id', $plan->id)
            ->where('assignee_type', $assigneeClass)
            ->where('assignee_id', $assigneeId)
            ->first();

        if ($existingAssignment) {
            return response()->json([
                'message' => 'An assignment already exists for this plan and assignee',
                'assignment' => $existingAssignment,
            ], 422);
        }

        $assignment = $plan->assignTo(
            $assignee,
            $validated['starts_at'] ?? null,
            $validated['ends_at'] ?? null
        );

        if (isset($validated['active'])) {
            $assignment->active = $validated['active'];
            $assignment->save();
        }

        return response()->json($assignment, 201);
    }

    /**
     * Remove an assignment.
     */
    public function removeAssignment(Request $request, BandwidthPlan $plan)
    {
        $validated = $request->validate([
            'assignment_id' => 'required|exists:bandwidth_assignments,id',
        ]);

        $assignment = BandwidthAssignment::findOrFail($validated['assignment_id']);

        // Check if the assignment belongs to this plan
        if ($assignment->assignable_type !== get_class($plan) || $assignment->assignable_id !== $plan->id) {
            return response()->json([
                'message' => 'Assignment does not belong to this plan',
            ], 403);
        }

        $assignment->delete();

        return response()->json([
            'message' => 'Assignment removed successfully',
        ]);
    }

    /**
     * Get plans with speeds in Mbps.
     */
    public function plansInMbps()
    {
        $plans = BandwidthPlan::active()->get()->map(function ($plan) {
            return [
                'id' => $plan->id,
                'name' => $plan->name,
                'description' => $plan->description,
                'download_speed_mbps' => $plan->getDownloadSpeedMbpsAttribute(),
                'upload_speed_mbps' => $plan->getUploadSpeedMbpsAttribute(),
                'burst_download_speed_mbps' => $plan->getBurstDownloadSpeedMbpsAttribute(),
                'burst_upload_speed_mbps' => $plan->getBurstUploadSpeedMbpsAttribute(),
                'burst_time' => $plan->burst_time,
                'priority' => $plan->priority,
                'active' => $plan->active,
            ];
        });

        return response()->json($plans);
    }

    /**
     * Clone an existing plan.
     */
    public function clone(Request $request, BandwidthPlan $plan)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:bandwidth_plans',
            'description' => 'nullable|string',
        ]);

        $newPlan = new BandwidthPlan;
        $newPlan->name = $validated['name'];
        $newPlan->description = $validated['description'] ?? $plan->description;
        $newPlan->download_speed = $plan->download_speed;
        $newPlan->upload_speed = $plan->upload_speed;
        $newPlan->burst_download_speed = $plan->burst_download_speed;
        $newPlan->burst_upload_speed = $plan->burst_upload_speed;
        $newPlan->burst_time = $plan->burst_time;
        $newPlan->priority = $plan->priority;
        $newPlan->active = true;
        $newPlan->save();

        return response()->json([
            'message' => 'Plan cloned successfully',
            'plan' => $newPlan,
        ], 201);
    }

    /**
     * Get plan statistics.
     */
    public function statistics()
    {
        $totalPlans = BandwidthPlan::count();
        $activePlans = BandwidthPlan::where('active', true)->count();

        $plansByPriority = BandwidthPlan::select('priority')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('priority')
            ->orderBy('priority')
            ->get();

        $plansWithMostAssignments = BandwidthPlan::withCount('assignments')
            ->orderBy('assignments_count', 'desc')
            ->limit(10)
            ->get();

        $avgDownloadSpeed = BandwidthPlan::avg('download_speed');
        $avgUploadSpeed = BandwidthPlan::avg('upload_speed');

        return response()->json([
            'total_plans' => $totalPlans,
            'active_plans' => $activePlans,
            'plans_by_priority' => $plansByPriority,
            'plans_with_most_assignments' => $plansWithMostAssignments,
            'average_download_speed' => $avgDownloadSpeed,
            'average_upload_speed' => $avgUploadSpeed,
        ]);
    }

    /**
     * Get dashboard statistics for bandwidth management.
     */
    public function dashboardStats()
    {
        // Get basic counts
        $totalPlans = BandwidthPlan::count();
        $activePlans = BandwidthPlan::where('active', true)->count();

        // Get assignments count
        $totalAssignments = BandwidthAssignment::count();
        $activeAssignments = BandwidthAssignment::where('active', true)->count();

        // Mock data for other stats (can be replaced with real data later)
        $totalPolicies = 0;
        $activePolicies = 0;
        $totalRules = 0;
        $activeRules = 0;
        $totalQuotas = 0;
        $activeQuotas = 0;
        $totalUsageRecords = 0;
        $totalDownload = 0;
        $totalUpload = 0;
        $totalBandwidth = 0;

        return response()->json([
            'total_plans' => $totalPlans,
            'active_plans' => $activePlans,
            'total_policies' => $totalPolicies,
            'active_policies' => $activePolicies,
            'total_rules' => $totalRules,
            'active_rules' => $activeRules,
            'total_quotas' => $totalQuotas,
            'active_quotas' => $activeQuotas,
            'total_assignments' => $totalAssignments,
            'active_assignments' => $activeAssignments,
            'total_usage_records' => $totalUsageRecords,
            'total_download' => $totalDownload,
            'total_upload' => $totalUpload,
            'total_bandwidth' => $totalBandwidth,
        ]);
    }
}
