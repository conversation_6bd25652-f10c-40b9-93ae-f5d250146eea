<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SystemSetting;
use App\Services\MpesaIdService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class SettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view admin settings');
    }

    /**
     * Display the settings management page.
     */
    public function index()
    {
        $settings = SystemSetting::all()->groupBy('group');

        return Inertia::render('admin/settings/index', [
            'settings' => $settings,
        ]);
    }

    /**
     * Display M-Pesa ID settings.
     */
    public function mpesaId()
    {
        $settings = SystemSetting::getGroup('mpesa_id');

        return Inertia::render('admin/settings/mpesa-id', [
            'settings' => $settings,
            'generationMethods' => [
                'sequential' => 'Sequential Numbers (MP1000, MP1001, MP1002...)',
                'customer_id' => 'Customer ID Based (CU0001, CU0002, CU0003...)',
                'phone_number' => 'Phone Number Based (PH123456, PH789012...)',
                'custom_format' => 'Custom Format (configurable pattern)',
            ],
            'assignmentModes' => [
                'auto' => 'Automatic Generation Only',
                'manual' => 'Manual Assignment Only',
                'hybrid' => 'Auto-generate with Manual Override',
            ],
        ]);
    }

    /**
     * Update M-Pesa ID settings.
     */
    public function updateMpesaId(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'mpesa_id_assignment_mode' => 'required|in:auto,manual,hybrid',
            'mpesa_id_generation_method' => 'required|in:sequential,customer_id,phone_number,custom_format',
            'mpesa_id_prefix' => 'nullable|string|max:10',
            'mpesa_id_suffix' => 'nullable|string|max:10',
            'mpesa_id_starting_number' => 'required|integer|min:1',
            'mpesa_id_padding' => 'required|integer|min:1|max:10',
            'mpesa_id_custom_format' => 'nullable|string|max:50',
            'mpesa_id_min_length' => 'required|integer|min:1|max:50',
            'mpesa_id_max_length' => 'required|integer|min:1|max:50',
            'mpesa_id_allowed_pattern' => 'nullable|string',
            'mpesa_id_auto_generate_on_create' => 'boolean',
            'mpesa_id_required_for_payments' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            // Validate custom format if using custom_format method
            if ($request->mpesa_id_generation_method === 'custom_format') {
                $this->validateCustomFormat($request->mpesa_id_custom_format);
            }

            // Validate regex pattern
            if ($request->mpesa_id_allowed_pattern) {
                $this->validateRegexPattern($request->mpesa_id_allowed_pattern);
            }

            // Update all settings
            foreach ($request->all() as $key => $value) {
                if (str_starts_with($key, 'mpesa_id_')) {
                    $type = $this->getSettingType($key);
                    SystemSetting::set($key, $value, $type, null, 'mpesa_id');
                }
            }

            return redirect()->route('admin.settings.mpesa-id')
                ->with('success', 'M-Pesa ID settings updated successfully.');

        } catch (\Exception $e) {

            return redirect()->back()
                ->with('error', 'Failed to update settings: '.$e->getMessage())
                ->withInput();
        }
    }

    /**
     * Bulk generate M-Pesa IDs for existing customers.
     */
    public function bulkGenerateMpesaIds()
    {
        try {
            $service = app(MpesaIdService::class);
            $results = $service->bulkGenerateForExistingCustomers();

            $message = "Bulk generation completed. Success: {$results['success']}, Failed: {$results['failed']}";

            if ($results['failed'] > 0) {
                return redirect()->back()
                    ->with('warning', $message)
                    ->with('errors', $results['errors']);
            }

            return redirect()->back()->with('success', $message);

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Bulk generation failed: '.$e->getMessage());
        }
    }

    /**
     * Preview M-Pesa ID generation with current settings.
     */
    public function previewMpesaId(Request $request)
    {
        try {
            // Create a temporary customer for preview
            $tempCustomer = new \App\Models\Customer([
                'id' => 9999,
                'name' => 'Preview Customer',
                'phone' => '254712345678',
            ]);

            $service = app(MpesaIdService::class);

            // Temporarily update settings for preview
            $originalSettings = [];
            foreach ($request->all() as $key => $value) {
                if (str_starts_with($key, 'mpesa_id_')) {
                    $originalSettings[$key] = SystemSetting::get($key);
                    $type = $this->getSettingType($key);
                    SystemSetting::set($key, $value, $type, null, 'mpesa_id');
                }
            }

            $previewId = $service->generateMpesaId($tempCustomer);

            // Restore original settings
            foreach ($originalSettings as $key => $value) {
                $type = $this->getSettingType($key);
                SystemSetting::set($key, $value, $type, null, 'mpesa_id');
            }

            return response()->json([
                'success' => true,
                'preview_id' => $previewId,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Validate custom format string.
     */
    protected function validateCustomFormat(?string $format): void
    {
        if (empty($format)) {
            throw new \InvalidArgumentException('Custom format cannot be empty when using custom_format method.');
        }

        $allowedPlaceholders = [
            '{prefix}', '{suffix}', '{customer_id}',
            '{phone_last_4}', '{phone_last_6}', '{sequential}',
        ];

        // Check if format contains at least one valid placeholder
        $hasValidPlaceholder = false;
        foreach ($allowedPlaceholders as $placeholder) {
            if (str_contains($format, $placeholder)) {
                $hasValidPlaceholder = true;
                break;
            }
        }

        if (! $hasValidPlaceholder) {
            throw new \InvalidArgumentException('Custom format must contain at least one valid placeholder: '.implode(', ', $allowedPlaceholders));
        }
    }

    /**
     * Validate regex pattern.
     */
    protected function validateRegexPattern(string $pattern): void
    {
        if (@preg_match($pattern, 'test') === false) {
            throw new \InvalidArgumentException('Invalid regex pattern provided.');
        }
    }

    /**
     * Get the appropriate type for a setting key.
     */
    protected function getSettingType(string $key): string
    {
        $booleanSettings = [
            'mpesa_id_auto_generate_on_create',
            'mpesa_id_required_for_payments',
        ];

        $integerSettings = [
            'mpesa_id_starting_number',
            'mpesa_id_padding',
            'mpesa_id_min_length',
            'mpesa_id_max_length',
        ];

        if (in_array($key, $booleanSettings)) {
            return 'boolean';
        }

        if (in_array($key, $integerSettings)) {
            return 'integer';
        }

        return 'string';
    }
}
