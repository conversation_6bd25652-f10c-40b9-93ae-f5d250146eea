<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SystemSetting;
use App\Services\CurrencyService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Inertia\Inertia;

class CurrencySettingsController extends Controller
{
    /**
     * Display currency settings page
     */
    public function index()
    {
        $settings = [
            'default_currency' => SystemSetting::where('key', 'default_currency')->first(),
            'currency_display_format' => SystemSetting::where('key', 'currency_display_format')->first(),
            'currency_decimal_places' => SystemSetting::where('key', 'currency_decimal_places')->first(),
        ];

        $supportedCurrencies = CurrencyService::getSupportedCurrencies();
        
        $displayFormats = [
            'symbol_before' => 'Symbol before amount ($100)',
            'symbol_after' => 'Symbol after amount (100$)',
            'code_before' => 'Code before amount (USD 100)',
            'code_after' => 'Code after amount (100 USD)',
        ];

        return Inertia::render('admin/currency-settings', [
            'settings' => $settings,
            'supportedCurrencies' => $supportedCurrencies,
            'displayFormats' => $displayFormats,
            'currentConfig' => CurrencyService::getJavaScriptConfig(),
        ]);
    }

    /**
     * Update currency settings
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            'default_currency' => 'required|string|in:USD,KES,ETB,UGX',
            'currency_display_format' => 'required|string|in:symbol_before,symbol_after,code_before,code_after',
            'currency_decimal_places' => 'required|integer|min:0|max:4',
        ]);

        // Update settings
        foreach ($validated as $key => $value) {
            SystemSetting::updateOrCreate(
                ['key' => $key],
                ['value' => $value]
            );
        }

        // Clear currency cache
        CurrencyService::clearCache();

        return redirect()->back()->with('success', 'Currency settings updated successfully.');
    }

    /**
     * Get currency configuration for API
     */
    public function getConfig()
    {
        return response()->json(CurrencyService::getJavaScriptConfig());
    }

    /**
     * Preview currency formatting
     */
    public function preview(Request $request)
    {
        $validated = $request->validate([
            'amount' => 'required|numeric',
            'currency' => 'required|string|in:USD,KES,ETB,UGX',
            'display_format' => 'required|string|in:symbol_before,symbol_after,code_before,code_after',
            'decimal_places' => 'required|integer|min:0|max:4',
        ]);

        $formatted = CurrencyService::format(
            $validated['amount'],
            $validated['currency'],
            [
                'display_format' => $validated['display_format'],
                'decimal_places' => $validated['decimal_places'],
            ]
        );

        return response()->json([
            'formatted' => $formatted,
            'currency_info' => CurrencyService::getCurrencyInfo($validated['currency']),
        ]);
    }

    /**
     * Reset currency settings to defaults
     */
    public function reset()
    {
        $defaultSettings = [
            'default_currency' => 'KES',
            'currency_display_format' => 'symbol_before',
            'currency_decimal_places' => '2',
        ];

        foreach ($defaultSettings as $key => $value) {
            SystemSetting::updateOrCreate(
                ['key' => $key],
                ['value' => $value]
            );
        }

        // Clear currency cache
        CurrencyService::clearCache();

        return redirect()->back()->with('success', 'Currency settings reset to defaults.');
    }
}
