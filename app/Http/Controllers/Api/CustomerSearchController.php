<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class CustomerSearchController extends Controller
{
    /**
     * Search customers with pagination and caching
     */
    public function search(Request $request): JsonResponse
    {
        $request->validate([
            'q' => 'required|string|min:2|max:100',
            'page' => 'integer|min:1',
            'per_page' => 'integer|min:1|max:100',
            'active_only' => 'boolean',
        ]);

        $query = trim($request->input('q'));
        $page = $request->input('page', 1);
        $perPage = min($request->input('per_page', 50), 100); // Cap at 100
        $activeOnly = $request->boolean('active_only', true);
        
        // Create cache key based on search parameters
        $cacheKey = "customer_search:" . md5(
            $query . $page . $perPage . ($activeOnly ? '1' : '0') . auth()->user()->organization_id
        );
        
        // Try to get from cache first (cache for 5 minutes)
        $result = Cache::remember($cacheKey, 300, function () use ($query, $page, $perPage, $activeOnly) {
            return $this->performSearch($query, $page, $perPage, $activeOnly);
        });

        return response()->json($result);
    }

    /**
     * Get a specific customer by ID
     */
    public function show(Request $request, $id): JsonResponse
    {
        $customer = Customer::where('organization_id', auth()->user()->organization_id)
            ->select('id', 'name', 'email', 'status')
            ->find($id);

        if (!$customer) {
            return response()->json(['error' => 'Customer not found'], 404);
        }

        return response()->json($customer);
    }

    /**
     * Perform the actual search query
     */
    private function performSearch(string $query, int $page, int $perPage, bool $activeOnly): array
    {
        $baseQuery = Customer::where('organization_id', auth()->user()->organization_id)
            ->select('id', 'name', 'email', 'status');

        // Apply status filter
        if ($activeOnly) {
            $baseQuery->where(function ($q) {
                $q->where('status', 'active')
                  ->orWhereNull('status');
            });
        }

        // Apply search filters
        $baseQuery->where(function ($q) use ($query) {
            $q->where('name', 'LIKE', "%{$query}%")
              ->orWhere('email', 'LIKE', "%{$query}%");
        });

        // Get total count for pagination info
        $total = $baseQuery->count();

        // Apply pagination and ordering
        $customers = $baseQuery
            ->orderBy('name')
            ->offset(($page - 1) * $perPage)
            ->limit($perPage)
            ->get();

        // Calculate pagination info
        $hasMore = ($page * $perPage) < $total;

        return [
            'data' => $customers->toArray(),
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'has_more' => $hasMore,
            'last_page' => ceil($total / $perPage),
        ];
    }

    /**
     * Get recent customers for quick access
     */
    public function recent(Request $request): JsonResponse
    {
        $request->validate([
            'limit' => 'integer|min:1|max:20',
        ]);

        $limit = $request->input('limit', 10);
        
        // Get recently created or updated customers
        $customers = Customer::where('organization_id', auth()->user()->organization_id)
            ->select('id', 'name', 'email', 'status')
            ->where(function ($q) {
                $q->where('status', 'active')
                  ->orWhereNull('status');
            })
            ->orderBy('updated_at', 'desc')
            ->limit($limit)
            ->get();

        return response()->json([
            'data' => $customers->toArray(),
            'total' => $customers->count(),
        ]);
    }

    /**
     * Get customer suggestions based on partial input
     */
    public function suggestions(Request $request): JsonResponse
    {
        $request->validate([
            'q' => 'required|string|min:1|max:50',
            'limit' => 'integer|min:1|max:10',
        ]);

        $query = trim($request->input('q'));
        $limit = $request->input('limit', 5);

        // Cache key for suggestions
        $cacheKey = "customer_suggestions:" . md5($query . $limit . auth()->user()->organization_id);
        
        $suggestions = Cache::remember($cacheKey, 600, function () use ($query, $limit) {
            return Customer::where('organization_id', auth()->user()->organization_id)
                ->select('id', 'name', 'email', 'status')
                ->where(function ($q) {
                    $q->where('status', 'active')
                      ->orWhereNull('status');
                })
                ->where(function ($q) use ($query) {
                    $q->where('name', 'LIKE', "{$query}%")
                      ->orWhere('email', 'LIKE', "{$query}%");
                })
                ->orderBy('name')
                ->limit($limit)
                ->get()
                ->toArray();
        });

        return response()->json([
            'data' => $suggestions,
            'total' => count($suggestions),
        ]);
    }

    /**
     * Clear customer search cache (for admin use)
     */
    public function clearCache(): JsonResponse
    {
        $pattern = "customer_search:*";
        $suggestionPattern = "customer_suggestions:*";
        
        // Note: This is a simplified cache clearing. In production, you might want
        // to use Redis SCAN or implement a more sophisticated cache tagging system
        Cache::flush(); // This clears all cache - use with caution
        
        return response()->json(['message' => 'Customer search cache cleared']);
    }
}
