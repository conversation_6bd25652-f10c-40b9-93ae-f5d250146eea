<?php

namespace App\Http\Controllers\Services;

use App\Models\Bandwidth\BandwidthPlan;
use App\Models\Customer;
use App\Models\Network\NetworkDevice;
use App\Models\Services\IpPool;
use App\Models\Services\StaticIpService;
use App\Models\Subscription;
use App\Services\MikrotikAddressListService;
use App\Services\QueryOptimizationService;
use App\Services\ValidationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class StaticIpServiceController extends ServiceController
{
    /**
     * Display a listing of Static IP services.
     */
    public function index(Request $request)
    {
        $query = QueryOptimizationService::optimizedStaticIpServicesQuery();

        // Apply filters
        if ($request->has('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        if ($request->has('device_id')) {
            $query->where('device_id', $request->device_id);
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('ip_pool_id')) {
            $query->where('ip_pool_id', $request->ip_pool_id);
        }

        // Apply search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('ip_address', 'like', "%{$search}%")
                    ->orWhere('comment', 'like', "%{$search}%")
                    ->orWhereHas('customer', function ($cq) use ($search) {
                        $cq->where('name', 'like', "%{$search}%");
                    });
            });
        }

        $services = $query->orderBy('created_at', 'desc')
            ->paginate(15);

        return Inertia::render('services/static-ip/index', [
            'services' => $services,
            'filters' => $request->only(['customer_id', 'device_id', 'status', 'ip_pool_id', 'search']),
        ]);
    }

    /**
     * Show the form for creating a new Static IP service.
     */
    public function create()
    {
        // Get customers with their active subscriptions and bandwidth plans
        $customers = Customer::active()->with(['subscriptions' => function ($query) {
            $query->where('status', 'active')->with('bandwidthPlan');
        }])->get();

        // Get all active network devices (assuming all are MikroTik routers)
        $devices = NetworkDevice::where('status', 'active')->get();

        // Get active IP pools with their DNS server configurations
        $ipPools = IpPool::active()->get();

        return Inertia::render('services/static-ip/create', [
            'customers' => $customers,
            'devices' => $devices,
            'ipPools' => $ipPools,
        ]);
    }

    /**
     * Get customer data with auto-populated subscription and bandwidth plan info.
     */
    public function getCustomerData(Customer $customer)
    {
        // Get the customer's active subscription
        $activeSubscription = $customer->subscriptions()
            ->where('status', 'active')
            ->with('bandwidthPlan')
            ->first();

        if (! $activeSubscription) {
            return response()->json([
                'success' => false,
                'error' => 'Customer has no active subscription',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'subscription' => [
                'id' => $activeSubscription->id,
                'name' => $activeSubscription->name,
                'price' => $activeSubscription->price,
                'bandwidth_plan' => $activeSubscription->bandwidthPlan ? [
                    'id' => $activeSubscription->bandwidthPlan->id,
                    'name' => $activeSubscription->bandwidthPlan->name,
                    'download_speed' => $activeSubscription->bandwidthPlan->download_speed,
                    'upload_speed' => $activeSubscription->bandwidthPlan->upload_speed,
                ] : null,
            ],
        ]);
    }

    /**
     * Get next available IP from a pool and auto-populate gateway.
     */
    public function getNextAvailableIp(IpPool $ipPool)
    {
        try {
            // Get next available IP from the pool
            $nextIp = $ipPool->getNextAvailableIp();

            if (! $nextIp) {
                return response()->json([
                    'success' => false,
                    'error' => 'No available IP addresses in this pool',
                ], 404);
            }

            // Auto-calculate gateway (assume .1 address of the same subnet)
            $ipParts = explode('.', $nextIp);
            $gateway = $ipParts[0].'.'.$ipParts[1].'.'.$ipParts[2].'.1';

            return response()->json([
                'success' => true,
                'ip_address' => $nextIp,
                'gateway' => $gateway,
                'subnet_mask' => $ipPool->subnet_mask,
                'dns_servers' => $ipPool->dns_servers ?? ['*******', '*******'],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get next available IP: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get all available IP addresses from a pool for dropdown selection.
     */
    public function getAvailableIps(IpPool $ipPool)
    {
        try {
            // Get all available IPs from the pool
            $availableIps = $ipPool->getAllAvailableIps();

            if (empty($availableIps)) {
                return response()->json([
                    'success' => false,
                    'error' => 'No available IP addresses in this pool',
                    'available_ips' => [],
                    'total_available' => 0,
                    'next_recommended' => null,
                ], 404);
            }

            // Get the next recommended IP (first available)
            $nextRecommended = $availableIps[0];

            // Auto-calculate gateway (assume .1 address of the same subnet)
            $ipParts = explode('.', $nextRecommended);
            $gateway = $ipParts[0].'.'.$ipParts[1].'.'.$ipParts[2].'.1';

            return response()->json([
                'success' => true,
                'available_ips' => $availableIps,
                'total_available' => count($availableIps),
                'next_recommended' => $nextRecommended,
                'gateway' => $gateway,
                'subnet_mask' => $ipPool->subnet_mask,
                'dns_servers' => $ipPool->dns_servers ?? ['*******', '*******'],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get available IPs: '.$e->getMessage(),
                'available_ips' => [],
                'total_available' => 0,
                'next_recommended' => null,
            ], 500);
        }
    }

    /**
     * Store a newly created Static IP service in storage.
     */
    public function store(Request $request)
    {
        // Sanitize input data
        $data = ValidationService::sanitizeData($request->all());

        // Validate input
        $validation = ValidationService::validateStaticIpServiceData($data);
        if ($validation->isError()) {
            return back()->withErrors($validation->getErrors())->withInput();
        }

        $validatedData = $validation->getData();

        // Get related models
        $customer = Customer::findOrFail($validatedData['customer_id']);
        $device = NetworkDevice::findOrFail($validatedData['device_id']);
        $ipPool = IpPool::findOrFail($validatedData['ip_pool_id']);

        // Auto-detect customer's active subscription
        $subscription = $customer->subscriptions()
            ->where('status', 'active')
            ->with('bandwidthPlan')
            ->first();

        if (! $subscription) {
            return back()->withErrors(['customer_id' => 'Customer has no active subscription.'])->withInput();
        }

        // Additional network validation
        $networkValidation = ValidationService::validateNetworkData($validatedData);
        if ($networkValidation->isError()) {
            return back()->withErrors($networkValidation->getErrors())->withInput();
        }

        // Prepare service configuration
        $config = [
            'device_id' => $validatedData['device_id'],
            'ip_pool_id' => $validatedData['ip_pool_id'],
            'bandwidth_plan_id' => $subscription->bandwidth_plan_id,
            'comment' => $validatedData['comment'] ?? '',
        ];

        if (isset($validatedData['ip_address']) && ! empty($validatedData['ip_address'])) {
            $config['ip_address'] = $validatedData['ip_address'];
        }

        // Create service using unified factory
        $serviceFactory = new \App\Services\ServiceProvisioningFactory;
        $result = $serviceFactory->createStaticIpService($customer, $subscription, $config);

        if ($result->isError()) {
            return back()->withErrors(['general' => $result->getMessage()])->withInput();
        }

        $serviceData = $result->getData();
        $service = $serviceData['service'];

        Log::info('Static IP service created via web interface', [
            'service_id' => $service->id,
            'customer_id' => $customer->id,
            'ip_address' => $service->ip_address,
            'created_by' => auth()->id(),
        ]);

        return redirect()
            ->route('services.static-ip.show', $service)
            ->with('success', 'Static IP service created successfully! IP assigned: '.$serviceData['ip_assigned']);
    }

    /**
     * Display the specified Static IP service.
     */
    public function show(StaticIpService $service)
    {
        $service->load(['customer', 'subscription', 'device', 'bandwidthPlan', 'ipPool']);

        return Inertia::render('services/static-ip/show', [
            'service' => $service,
        ]);
    }

    /**
     * Show the form for editing the specified Static IP service.
     */
    public function edit(StaticIpService $service)
    {
        $service->load(['customer', 'subscription', 'device', 'bandwidthPlan', 'ipPool']);

        $customers = Customer::active()->get();
        $devices = NetworkDevice::where('status', 'active')->get();
        $bandwidthPlans = BandwidthPlan::active()->get();
        $ipPools = IpPool::active()->get();

        return Inertia::render('services/static-ip/edit', [
            'service' => $service,
            'customers' => $customers,
            'devices' => $devices,
            'bandwidthPlans' => $bandwidthPlans,
            'ipPools' => $ipPools,
        ]);
    }

    /**
     * Update the specified Static IP service in storage.
     */
    public function update(Request $request, StaticIpService $service)
    {
        $validator = Validator::make($request->all(), [
            'gateway' => 'nullable|ip',
            'dns_servers' => 'nullable|array',
            'bandwidth_plan_id' => 'nullable|exists:bandwidth_plans,id',
            'comment' => 'nullable|string',
            'status' => 'nullable|in:active,suspended',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // Get related models
        $device = $service->device;
        $bandwidthPlan = ($request->bandwidth_plan_id && $request->bandwidth_plan_id !== 'none') ? BandwidthPlan::findOrFail($request->bandwidth_plan_id) : null;

        // Validate models
        if (! $this->validateDevice($device)) {
            return redirect()->back()->withErrors(['device_id' => 'Device is not a valid MikroTik router.'])->withInput();
        }

        if ($bandwidthPlan && ! $this->validateBandwidthPlan($bandwidthPlan)) {
            return redirect()->back()->withErrors(['bandwidth_plan_id' => 'Bandwidth plan is not active.'])->withInput();
        }

        // Update service in database
        $service->gateway = $request->gateway ?? $service->gateway;
        $service->dns_servers = $request->dns_servers ?? $service->dns_servers;
        $service->bandwidth_plan_id = $bandwidthPlan ? $bandwidthPlan->id : null;
        $service->comment = $request->comment;

        // Handle status change specially (suspend/activate)
        $oldStatus = $service->status;
        $newStatus = $request->status ?? $oldStatus;

        if ($request->has('status') && $newStatus !== $oldStatus) {
            // Status change - handle MikroTik operations first, then update database
            try {
                if ($newStatus === 'suspended' && $oldStatus === 'active') {
                    // Suspend operation
                    $success = $this->suspendStaticIpOnMikrotik($service);
                    if (! $success) {
                        throw new \Exception('Failed to suspend service on MikroTik');
                    }

                    // Update subscription status to suspended if this service has a subscription
                    $this->updateSubscriptionStatus($service, 'suspended');

                } elseif ($newStatus === 'active' && $oldStatus === 'suspended') {
                    // Activate operation
                    $success = $this->activateStaticIpOnMikrotik($service);
                    if (! $success) {
                        throw new \Exception('Failed to activate service on MikroTik');
                    }

                    // Update subscription status to active if this service has a subscription
                    $this->updateSubscriptionStatus($service, 'active');
                }

                $this->logActivity('Static IP service status changed', [
                    'service_id' => $service->id,
                    'old_status' => $oldStatus,
                    'new_status' => $newStatus,
                ]);

                return redirect()->route('services.static-ip.show', $service->id)
                    ->with('success', 'Static IP service status updated successfully.');

            } catch (\Exception $e) {
                Log::error('Failed to update Static IP service status on MikroTik', [
                    'service_id' => $service->id,
                    'old_status' => $oldStatus,
                    'new_status' => $newStatus,
                    'error' => $e->getMessage(),
                ]);

                return redirect()->route('services.static-ip.show', $service->id)
                    ->with('error', 'Failed to update service status: '.$e->getMessage());
            }
        } else {
            // Non-status updates - update database first, then MikroTik
            $service->save();

            // Update the service on the MikroTik router
            try {
                $this->updateStaticIpOnMikrotik($service);

                $this->logActivity('Static IP service updated', [
                    'service_id' => $service->id,
                    'ip_address' => $service->ip_address,
                ]);

                return redirect()->route('services.static-ip.show', $service->id)
                    ->with('success', 'Static IP service updated successfully.');
            } catch (\Exception $e) {
                Log::error('Failed to update Static IP service on MikroTik', [
                    'service_id' => $service->id,
                    'error' => $e->getMessage(),
                ]);

                return redirect()->route('services.static-ip.show', $service->id)
                    ->with('warning', 'Static IP service updated in database but failed to update on router: '.$e->getMessage());
            }
        }
    }

    /**
     * Remove the specified Static IP service from storage.
     */
    public function destroy(StaticIpService $service)
    {
        try {
            // Remove from MikroTik first
            $this->removeStaticIpFromMikrotik($service);

            // Then remove from database
            $service->delete();

            $this->logActivity('Static IP service deleted', [
                'service_id' => $service->id,
                'ip_address' => $service->ip_address,
            ]);

            return redirect()->route('services.static-ip.index')
                ->with('success', 'Static IP service deleted successfully.');
        } catch (\Exception $e) {
            Log::error('Failed to delete Static IP service from MikroTik', [
                'service_id' => $service->id,
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('services.static-ip.show', $service->id)
                ->with('warning', 'Failed to delete Static IP service from router: '.$e->getMessage());
        }
    }

    /**
     * Provision a Static IP service on a MikroTik router.
     */
    protected function provisionStaticIpOnMikrotik(StaticIpService $service)
    {
        $device = $service->device;

        if (! $this->testDeviceConnection($device)) {
            throw new \Exception('Cannot connect to MikroTik device.');
        }

        // 1. Add static route
        $routeCommand = '/ip/route/add';
        $routeParams = [
            'dst-address' => $service->ip_address.'/32', // Use /32 for single IP
            'gateway' => $service->gateway,
            'comment' => "Customer: {$service->customer->name}, ID: {$service->customer->id}",
        ];

        $routeResult = $device->executeMikrotikCommand($routeCommand, $routeParams);

        // Store the MikroTik route ID for future reference
        if (isset($routeResult['.id'])) {
            $service->mikrotik_route_id = $routeResult['.id'];
            $service->save();
        }

        // 2. Add NAT rule for outgoing traffic (NAT is sufficient, no need for firewall ACCEPT rules)
        $natCommand = '/ip/firewall/nat/add';
        $natParams = [
            'chain' => 'srcnat',
            'src-address' => $service->ip_address.'/32', // Use /32 for single IP
            'action' => 'masquerade',
            'comment' => "NAT for customer {$service->customer->name}",
        ];

        $natResult = $device->executeMikrotikCommand($natCommand, $natParams);

        // Store the MikroTik NAT ID for future reference
        if (isset($natResult['.id'])) {
            $service->mikrotik_nat_id = $natResult['.id'];
            $service->save();
        }

        // 3. Add bandwidth limitation if a plan is specified
        if ($service->bandwidthPlan) {
            $this->addBandwidthLimitation($service);
        }

        return true;
    }

    /**
     * Update a Static IP service on a MikroTik router.
     */
    protected function updateStaticIpOnMikrotik(StaticIpService $service)
    {
        $device = $service->device;

        if (! $this->testDeviceConnection($device)) {
            throw new \Exception('Cannot connect to MikroTik device.');
        }

        // If the service doesn't have MikroTik IDs and is not suspended, provision it from scratch
        if (! $service->mikrotik_route_id && ! $service->mikrotik_nat_id) {
            if ($service->status === 'suspended') {
                // For suspended services without MikroTik IDs, just add the blocking rule
                return $this->suspendStaticIpOnMikrotik($service);
            } else {
                // For active services without MikroTik IDs, check if there are existing blocking rules first
                $device = $service->device;
                if ($this->testDeviceConnection($device)) {
                    $existingBlockingRules = $device->executeMikrotikCommand('/ip/firewall/filter/print', [
                        '?src-address' => $service->ip_address.'/32',
                        '?action' => 'drop',
                        '?chain' => 'forward',
                    ]);

                    if (! empty($existingBlockingRules)) {
                        // There are existing blocking rules, just disable them instead of provisioning
                        return $this->activateStaticIpOnMikrotik($service);
                    }
                }

                // No existing blocking rules, provision from scratch
                return $this->provisionStaticIpOnMikrotik($service);
            }
        }

        // 1. Update static route if it exists
        if ($service->mikrotik_route_id) {
            try {
                $routeCommand = '/ip/route/set';
                $routeParams = [
                    '.id' => $service->mikrotik_route_id,
                    'gateway' => $service->gateway,
                    'comment' => "Customer: {$service->customer->name}, ID: {$service->customer->id}",
                ];

                $device->executeMikrotikCommand($routeCommand, $routeParams);
            } catch (\Exception $e) {
                Log::warning('Failed to update static route', [
                    'service_id' => $service->id,
                    'error' => $e->getMessage(),
                ]);
                // Continue with other updates
            }
        }

        // 2. Update bandwidth limitation if a plan is specified
        if ($service->bandwidthPlan) {
            $this->updateBandwidthLimitation($service);
        }

        // 3. Handle service status
        if ($service->status === 'suspended') {
            $this->suspendStaticIpOnMikrotik($service);
        } else {
            $this->activateStaticIpOnMikrotik($service);
        }

        return true;
    }

    /**
     * Remove a Static IP service from a MikroTik router.
     */
    protected function removeStaticIpFromMikrotik(StaticIpService $service)
    {
        $device = $service->device;

        if (! $this->testDeviceConnection($device)) {
            throw new \Exception('Cannot connect to MikroTik device.');
        }

        // 1. Remove NAT rule if it exists
        if ($service->mikrotik_nat_id) {
            try {
                $device->executeMikrotikCommand('/ip/firewall/nat/remove', [
                    '.id' => $service->mikrotik_nat_id,
                ]);
            } catch (\Exception $e) {
                Log::warning('Failed to remove NAT rule', [
                    'service_id' => $service->id,
                    'error' => $e->getMessage(),
                ]);
                // Continue with other removals
            }
        }

        // 2. Remove static route if it exists
        if ($service->mikrotik_route_id) {
            try {
                $device->executeMikrotikCommand('/ip/route/remove', [
                    '.id' => $service->mikrotik_route_id,
                ]);
            } catch (\Exception $e) {
                Log::warning('Failed to remove static route', [
                    'service_id' => $service->id,
                    'error' => $e->getMessage(),
                ]);
                // Continue with other removals
            }
        }

        // 3. Remove bandwidth limitation if it exists
        $this->removeBandwidthLimitation($service);

        return true;
    }

    /**
     * Add bandwidth limitation for a Static IP service.
     */
    protected function addBandwidthLimitation(StaticIpService $service)
    {
        $device = $service->device;
        $plan = $service->bandwidthPlan;

        if (! $plan) {
            return false;
        }

        try {
            // Add simple queue for bandwidth limitation
            $queueCommand = '/queue/simple/add';
            $queueParams = [
                'name' => "customer-{$service->customer_id}-{$service->id}",
                'target' => $service->ip_address.'/32', // Use /32 for single IP
                'max-limit' => "{$plan->download_speed}M/{$plan->upload_speed}M", // Use M for Mbps
                'comment' => "Bandwidth limit for customer {$service->customer->name}",
            ];

            $device->executeMikrotikCommand($queueCommand, $queueParams);

            return true;
        } catch (\Exception $e) {
            Log::warning('Failed to add bandwidth limitation', [
                'service_id' => $service->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Update bandwidth limitation for a Static IP service.
     */
    protected function updateBandwidthLimitation(StaticIpService $service)
    {
        $device = $service->device;
        $plan = $service->bandwidthPlan;

        if (! $plan) {
            return false;
        }

        try {
            // Find existing queue
            $queues = $device->executeMikrotikCommand('/queue/simple/print', [
                '?target' => $service->ip_address.'/32', // Use /32 for single IP
            ]);

            if (empty($queues)) {
                // Queue doesn't exist, create it
                return $this->addBandwidthLimitation($service);
            }

            $queue = $queues[0];
            $queueId = $queue['.id'];

            // Update queue
            $queueCommand = '/queue/simple/set';
            $queueParams = [
                '.id' => $queueId,
                'max-limit' => "{$plan->download_speed}M/{$plan->upload_speed}M", // Use M for Mbps
                'comment' => "Bandwidth limit for customer {$service->customer->name}",
            ];

            $device->executeMikrotikCommand($queueCommand, $queueParams);

            return true;
        } catch (\Exception $e) {
            Log::warning('Failed to update bandwidth limitation', [
                'service_id' => $service->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Remove bandwidth limitation for a Static IP service.
     */
    protected function removeBandwidthLimitation(StaticIpService $service)
    {
        $device = $service->device;

        try {
            // Find existing queue
            $queues = $device->executeMikrotikCommand('/queue/simple/print', [
                '?target' => $service->ip_address.'/32', // Use /32 for single IP
            ]);

            if (empty($queues)) {
                // Queue doesn't exist, nothing to do
                return true;
            }

            $queue = $queues[0];
            $queueId = $queue['.id'];

            // Remove queue
            $device->executeMikrotikCommand('/queue/simple/remove', [
                '.id' => $queueId,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::warning('Failed to remove bandwidth limitation', [
                'service_id' => $service->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Suspend a Static IP service on a MikroTik router (simplified).
     */
    protected function suspendStaticIpOnMikrotik(StaticIpService $service)
    {
        // Only allow suspension of active services
        if ($service->status !== 'active') {
            Log::warning('Attempted to suspend non-active service', [
                'service_id' => $service->id,
                'current_status' => $service->status,
            ]);

            return false;
        }

        $device = $service->device;
        $addressListService = new MikrotikAddressListService;

        Log::info('Suspending Static IP service', [
            'service_id' => $service->id,
            'customer_name' => $service->customer->name,
            'ip_address' => $service->ip_address,
            'device_id' => $device->id,
        ]);

        try {
            // Test device connection
            if (! $this->testDeviceConnection($device)) {
                throw new \Exception('Cannot connect to MikroTik device');
            }

            // Suspend customer (single API call)
            $mikrotikId = $addressListService->suspendCustomer(
                $device,
                $service->ip_address,
                $service->customer->name
            );

            if (! $mikrotikId) {
                throw new \Exception('Failed to suspend customer on MikroTik');
            }

            // Store MikroTik ID and update status
            $service->mikrotik_id = $mikrotikId;
            $service->status = 'suspended';
            $service->save();

            // Disable bandwidth queues
            $this->disableCustomerQueues($service, $device);

            Log::info('Static IP service suspended successfully', [
                'service_id' => $service->id,
                'mikrotik_id' => $mikrotikId,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to suspend Static IP service', [
                'service_id' => $service->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Remove legacy individual firewall rules for this IP (cleanup).
     */
    protected function removeLegacyFirewallRules(StaticIpService $service)
    {
        $device = $service->device;

        try {
            // Find any existing individual firewall rules for this IP
            $existingRules = $device->executeMikrotikCommand('/ip/firewall/filter/print', [
                '?src-address' => $service->ip_address.'/32',
                '?action' => 'drop',
                '?chain' => 'forward',
            ]);

            Log::info('Found legacy firewall rules to remove', [
                'service_id' => $service->id,
                'ip_address' => $service->ip_address,
                'rules_count' => count($existingRules),
            ]);

            foreach ($existingRules as $rule) {
                $device->executeMikrotikCommand('/ip/firewall/filter/remove', [
                    '.id' => $rule['.id'],
                ]);

                Log::info('Removed legacy firewall rule', [
                    'service_id' => $service->id,
                    'rule_id' => $rule['.id'],
                    'rule_comment' => $rule['comment'] ?? 'no comment',
                ]);
            }

            // Clear the stored firewall ID since we removed the rule
            if ($service->mikrotik_firewall_id) {
                $service->mikrotik_firewall_id = null;
                $service->save();
            }

        } catch (\Exception $e) {
            Log::warning('Failed to remove legacy firewall rules', [
                'service_id' => $service->id,
                'ip_address' => $service->ip_address,
                'error' => $e->getMessage(),
            ]);
            // Don't throw - this is cleanup, not critical
        }
    }

    /**
     * Disable bandwidth queues for a customer.
     */
    protected function disableCustomerQueues(StaticIpService $service, $device)
    {
        $queues = $device->executeMikrotikCommand('/queue/simple/print', [
            '?target' => $service->ip_address.'/32',
        ]);

        Log::info('Found bandwidth queues for IP', [
            'service_id' => $service->id,
            'ip_address' => $service->ip_address,
            'queues_count' => count($queues),
            'queues' => $queues,
        ]);

        foreach ($queues as $queue) {
            $device->executeMikrotikCommand('/queue/simple/set', [
                '.id' => $queue['.id'],
                'disabled' => 'yes',
            ]);

            Log::info('Disabled bandwidth queue', [
                'service_id' => $service->id,
                'queue_id' => $queue['.id'],
                'queue_name' => $queue['name'] ?? 'unnamed',
            ]);
        }
    }

    /**
     * Enable bandwidth queues for a customer.
     */
    protected function enableCustomerQueues(StaticIpService $service, $device)
    {
        $queues = $device->executeMikrotikCommand('/queue/simple/print', [
            '?target' => $service->ip_address.'/32',
        ]);

        Log::info('Found bandwidth queues for IP to enable', [
            'service_id' => $service->id,
            'ip_address' => $service->ip_address,
            'queues_count' => count($queues),
            'queues' => $queues,
        ]);

        foreach ($queues as $queue) {
            $device->executeMikrotikCommand('/queue/simple/set', [
                '.id' => $queue['.id'],
                'disabled' => 'no',
            ]);

            Log::info('Enabled bandwidth queue', [
                'service_id' => $service->id,
                'queue_id' => $queue['.id'],
                'queue_name' => $queue['name'] ?? 'unnamed',
            ]);
        }
    }

    /**
     * Activate a suspended Static IP service on a MikroTik router (simplified).
     */
    protected function activateStaticIpOnMikrotik(StaticIpService $service)
    {
        // Only allow activation of suspended services
        if ($service->status !== 'suspended') {
            Log::warning('Attempted to activate non-suspended service', [
                'service_id' => $service->id,
                'current_status' => $service->status,
            ]);

            return false;
        }

        // Must have stored MikroTik ID for removal
        if (! $service->mikrotik_id) {
            Log::error('Cannot activate service - no MikroTik ID stored', [
                'service_id' => $service->id,
            ]);

            return false;
        }

        $device = $service->device;
        $addressListService = new MikrotikAddressListService;

        Log::info('Activating Static IP service', [
            'service_id' => $service->id,
            'customer_name' => $service->customer->name,
            'ip_address' => $service->ip_address,
            'mikrotik_id' => $service->mikrotik_id,
        ]);

        try {
            // Test device connection
            if (! $this->testDeviceConnection($device)) {
                throw new \Exception('Cannot connect to MikroTik device');
            }

            // Reactivate customer (single API call using stored ID)
            $success = $addressListService->reactivateCustomer(
                $device,
                $service->mikrotik_id,
                $service->customer->name
            );

            if (! $success) {
                throw new \Exception('Failed to reactivate customer on MikroTik');
            }

            // Clear MikroTik ID and update status
            $service->mikrotik_id = null;
            $service->status = 'active';
            $service->save();

            // Enable bandwidth queues
            $this->enableCustomerQueues($service, $device);

            Log::info('Static IP service activated successfully', [
                'service_id' => $service->id,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to activate Static IP service', [
                'service_id' => $service->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Update subscription status when service status changes manually.
     */
    protected function updateSubscriptionStatus(StaticIpService $service, string $status): void
    {
        if (! $service->subscription_id) {
            Log::info('Service has no subscription, skipping subscription status update', [
                'service_id' => $service->id,
                'service_status' => $status,
            ]);

            return;
        }

        try {
            $subscription = $service->subscription;

            if (! $subscription) {
                Log::warning('Subscription not found for service', [
                    'service_id' => $service->id,
                    'subscription_id' => $service->subscription_id,
                ]);

                return;
            }

            $oldStatus = $subscription->status;

            // Only update if status is actually changing
            if ($oldStatus === $status) {
                Log::info('Subscription status already matches service status', [
                    'service_id' => $service->id,
                    'subscription_id' => $subscription->id,
                    'status' => $status,
                ]);

                return;
            }

            // Update subscription status and timestamps
            $subscription->status = $status;

            if ($status === 'suspended') {
                $subscription->suspension_date = now();
                $subscription->reactivation_date = null;
            } elseif ($status === 'active') {
                $subscription->reactivation_date = now();
            }

            $subscription->save();

            Log::info('Subscription status updated to match service status', [
                'service_id' => $service->id,
                'subscription_id' => $subscription->id,
                'old_status' => $oldStatus,
                'new_status' => $status,
                'customer_name' => $service->customer->name,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update subscription status', [
                'service_id' => $service->id,
                'subscription_id' => $service->subscription_id,
                'target_status' => $status,
                'error' => $e->getMessage(),
            ]);
            // Don't throw - service status change should still succeed
        }
    }
}
