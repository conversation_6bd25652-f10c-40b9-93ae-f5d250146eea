<?php

namespace App\Http\Controllers\Services;

use App\Models\Bandwidth\BandwidthPlan;
use App\Models\Customer;
use App\Models\Network\NetworkDevice;
use App\Models\Services\PppoeService;
use App\Models\Subscription;
use App\Services\ProgressTracker;
use App\Services\QueryOptimizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class PppoeServiceController extends ServiceController
{
    /**
     * Display a listing of PPPoE services.
     */
    public function index(Request $request)
    {
        $query = QueryOptimizationService::optimizedPppoeServicesQuery();

        // Apply filters
        if ($request->has('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        if ($request->has('device_id')) {
            $query->where('device_id', $request->device_id);
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Apply search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('username', 'like', "%{$search}%")
                    ->orWhere('ip_address', 'like', "%{$search}%")
                    ->orWhere('comment', 'like', "%{$search}%")
                    ->orWhereHas('customer', function ($cq) use ($search) {
                        $cq->where('name', 'like', "%{$search}%");
                    });
            });
        }

        $services = $query->orderBy('created_at', 'desc')
            ->paginate(15);

        return Inertia::render('services/pppoe/index', [
            'services' => $services,
            'filters' => $request->only(['customer_id', 'device_id', 'status', 'search']),
        ]);
    }

    /**
     * Show the form for creating a new PPPoE service.
     */
    public function create()
    {
        // Get all active customers with their subscriptions
        $customers = Customer::active()
            ->with(['subscriptions' => function ($query) {
                $query->where('status', 'active');
            }])
            ->orderBy('name')
            ->get();

        // Get all MikroTik devices (fix device detection issue)
        $devices = NetworkDevice::whereIn('type', ['mikrotik', 'router'])
            ->orWhere('name', 'like', '%mikrotik%')
            ->orWhere('name', 'like', '%router%')
            ->orderBy('name')
            ->get();

        // Get all active bandwidth plans
        $bandwidthPlans = BandwidthPlan::active()
            ->orderBy('name')
            ->get();

        return Inertia::render('services/pppoe/create', [
            'customers' => $customers,
            'devices' => $devices,
            'bandwidthPlans' => $bandwidthPlans,
        ]);
    }

    /**
     * Get customer data for auto-population.
     */
    public function getCustomerData(Customer $customer)
    {
        try {
            $customer->load(['subscriptions' => function ($query) {
                $query->where('status', 'active');
            }]);

            $subscription = $customer->subscriptions->first();
            $bandwidthPlan = null;

            // Try to find a matching bandwidth plan based on subscription name
            if ($subscription) {
                $bandwidthPlan = BandwidthPlan::where('name', 'like', '%'.$subscription->name.'%')
                    ->orWhere('price', $subscription->price)
                    ->first();
            }

            return response()->json([
                'success' => true,
                'subscription' => $subscription,
                'bandwidthPlan' => $bandwidthPlan,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get customer data: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate username and password for PPPoE service.
     */
    public function generateCredentials(Customer $customer)
    {
        try {
            $username = PppoeService::generateUsername($customer);
            $password = PppoeService::generatePassword();

            return response()->json([
                'success' => true,
                'username' => $username,
                'password' => $password,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to generate credentials: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Store a newly created PPPoE service in storage (streamlined approach).
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'customer_id' => 'required|exists:customers,id',
            'device_id' => 'required|exists:network_devices,id',
            'username' => 'required|string|unique:pppoe_services,username',
            'password' => 'required|string|min:6',
            'service_profile' => 'nullable|string',
            'bandwidth_plan_id' => 'nullable|exists:bandwidth_plans,id',
            'comment' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            // Get related models
            $customer = Customer::findOrFail($request->customer_id);
            $device = NetworkDevice::findOrFail($request->device_id);
            $bandwidthPlan = ($request->bandwidth_plan_id && $request->bandwidth_plan_id !== 'none')
                ? BandwidthPlan::findOrFail($request->bandwidth_plan_id)
                : null;

            // Create PPPoE service in database (without subscription dependency)
            $service = PppoeService::create([
                'customer_id' => $customer->id,
                'subscription_id' => null, // Remove subscription dependency
                'device_id' => $device->id,
                'username' => $request->username,
                'password' => $request->password,
                'service_profile' => $request->service_profile,
                'bandwidth_plan_id' => $bandwidthPlan ? $bandwidthPlan->id : null,
                'status' => 'pending', // Start as pending until provisioned
                'comment' => $request->comment,
            ]);

            // Provision the service synchronously on MikroTik
            $provisioningResult = $this->provisionPppoeServiceOnMikrotik($service);

            if ($provisioningResult['success']) {
                $service->status = 'active';
                $service->mikrotik_id = $provisioningResult['mikrotik_id'];
                $service->save();

                DB::commit();

                Log::info('PPPoE service created and provisioned successfully', [
                    'service_id' => $service->id,
                    'customer_id' => $customer->id,
                    'customer_name' => $customer->name,
                    'username' => $service->username,
                    'device_id' => $device->id,
                    'mikrotik_id' => $provisioningResult['mikrotik_id'],
                ]);

                return redirect()->route('services.pppoe.show', $service->id)
                    ->with('success', 'PPPoE service created and provisioned successfully!');
            } else {
                $service->status = 'provisioning_failed';
                $service->save();

                DB::commit();

                Log::error('PPPoE service created but provisioning failed', [
                    'service_id' => $service->id,
                    'error' => $provisioningResult['error'],
                ]);

                return redirect()->route('services.pppoe.show', $service->id)
                    ->with('warning', 'PPPoE service created but provisioning failed: '.$provisioningResult['error']);
            }

        } catch (\Exception $e) {
            DB::rollback();

            Log::error('Failed to create PPPoE service', [
                'customer_id' => $request->customer_id,
                'username' => $request->username,
                'error' => $e->getMessage(),
            ]);

            return redirect()->back()
                ->withErrors(['error' => 'Failed to create PPPoE service: '.$e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Display the specified PPPoE service.
     */
    public function show(PppoeService $service)
    {
        $service->load(['customer', 'subscription', 'device', 'bandwidthPlan']);

        // Get active session information if available
        $activeSession = null;
        try {
            $activeSession = $this->getActiveSession($service);
        } catch (\Exception $e) {
            Log::error('Failed to get active session for PPPoE service', [
                'service_id' => $service->id,
                'error' => $e->getMessage(),
            ]);
        }

        return Inertia::render('services/pppoe/show', [
            'service' => $service,
            'activeSession' => $activeSession,
        ]);
    }

    /**
     * Show the form for editing the specified PPPoE service.
     */
    public function edit(PppoeService $service)
    {
        $service->load(['customer', 'subscription', 'device', 'bandwidthPlan']);

        $customers = Customer::active()->get();
        $devices = NetworkDevice::where('type', 'mikrotik')->orWhere('type', 'router')->get();
        $bandwidthPlans = BandwidthPlan::active()->get();

        return Inertia::render('services/pppoe/edit', [
            'service' => $service,
            'customers' => $customers,
            'devices' => $devices,
            'bandwidthPlans' => $bandwidthPlans,
        ]);
    }

    /**
     * Update the specified PPPoE service in storage.
     */
    public function update(Request $request, PppoeService $service)
    {
        $validator = Validator::make($request->all(), [
            'password' => 'nullable|string|min:6',
            'service_profile' => 'nullable|string',
            'bandwidth_plan_id' => 'nullable|exists:bandwidth_plans,id',
            'comment' => 'nullable|string',
            'status' => 'nullable|in:active,suspended',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // Get related models
        $device = $service->device;
        $bandwidthPlan = ($request->bandwidth_plan_id && $request->bandwidth_plan_id !== 'none') ? BandwidthPlan::findOrFail($request->bandwidth_plan_id) : null;

        // Validate models
        if (! $this->validateDevice($device)) {
            return redirect()->back()->withErrors(['device_id' => 'Device is not a valid MikroTik router.'])->withInput();
        }

        if ($bandwidthPlan && ! $this->validateBandwidthPlan($bandwidthPlan)) {
            return redirect()->back()->withErrors(['bandwidth_plan_id' => 'Bandwidth plan is not active.'])->withInput();
        }

        // Update service in database
        $service->password = $request->password ?? $service->password;
        $service->service_profile = $request->service_profile;
        $service->bandwidth_plan_id = $bandwidthPlan ? $bandwidthPlan->id : null;
        $service->comment = $request->comment;

        // Handle status change
        $oldStatus = $service->status;
        $newStatus = $request->status ?? $oldStatus;

        if ($request->has('status') && $newStatus !== $oldStatus) {
            $service->status = $newStatus;

            // Update subscription status to match service status
            $this->updateSubscriptionStatus($service, $newStatus);
        }

        $service->save();

        // Update the service on the MikroTik router
        try {
            $this->updatePppoeOnMikrotik($service);

            $this->logActivity('PPPoE service updated', [
                'service_id' => $service->id,
                'username' => $service->username,
            ]);

            return redirect()->route('services.pppoe.show', $service->id)
                ->with('success', 'PPPoE service updated successfully.');
        } catch (\Exception $e) {
            Log::error('Failed to update PPPoE service on MikroTik', [
                'service_id' => $service->id,
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('services.pppoe.show', $service->id)
                ->with('warning', 'PPPoE service updated in database but failed to update on router: '.$e->getMessage());
        }
    }

    /**
     * Remove the specified PPPoE service from storage.
     */
    public function destroy(PppoeService $service)
    {
        try {
            // Remove from MikroTik first
            $this->removePppoeFromMikrotik($service);

            // Then remove from database
            $service->delete();

            $this->logActivity('PPPoE service deleted', [
                'service_id' => $service->id,
                'username' => $service->username,
            ]);

            return redirect()->route('services.pppoe.index')
                ->with('success', 'PPPoE service deleted successfully.');
        } catch (\Exception $e) {
            Log::error('Failed to delete PPPoE service from MikroTik', [
                'service_id' => $service->id,
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('services.pppoe.show', $service->id)
                ->with('warning', 'Failed to delete PPPoE service from router: '.$e->getMessage());
        }
    }

    /**
     * Provision a PPPoE service on a MikroTik router with progress tracking.
     */
    protected function provisionPppoeOnMikrotikWithProgress(PppoeService $service, ProgressTracker $progress)
    {
        $device = $service->device;

        // Step 2: Connect to MikroTik device
        $progress->updateProgress(1, 'Connecting to MikroTik device...', 'in_progress');

        if (! $this->testDeviceConnection($device)) {
            throw new \Exception('Cannot connect to MikroTik device.');
        }

        $progress->completeStep('Connected to MikroTik device');

        // Prepare command to add PPPoE secret
        $command = '/ppp/secret/add';
        $params = [
            'name' => $service->username,
            'password' => $service->password,
            'service' => 'pppoe',
            'profile' => $service->service_profile ?? 'default',
            'comment' => "Customer: {$service->customer->name}, ID: {$service->customer->id}",
        ];

        // Step 3: Check existing profiles
        $progress->updateProgress(2, 'Checking existing profiles...', 'in_progress');

        // Add bandwidth limits if a plan is specified
        if ($service->bandwidthPlan) {
            $plan = $service->bandwidthPlan;

            // If the service profile doesn't exist, create it
            $profileName = "plan-{$plan->id}";
            $profileExists = false;

            try {
                $profiles = $device->executeMikrotikCommand('/ppp/profile/print');
                foreach ($profiles as $profile) {
                    if ($profile['name'] === $profileName) {
                        $profileExists = true;
                        break;
                    }
                }

                $progress->completeStep('Profile check completed');

                if (! $profileExists) {
                    // Step 4: Create bandwidth profile
                    $progress->updateProgress(3, "Creating bandwidth profile '{$profileName}'...", 'in_progress');

                    // Create a new profile with bandwidth limits
                    $device->executeMikrotikCommand('/ppp/profile/add', [
                        'name' => $profileName,
                        'rate-limit' => "{$plan->download_speed}M/{$plan->upload_speed}M", // Use M for Mbps
                        'comment' => "Bandwidth Plan: {$plan->name}",
                    ]);

                    $progress->completeStep("Bandwidth profile '{$profileName}' created");
                } else {
                    $progress->completeStep("Bandwidth profile '{$profileName}' already exists");
                }

                // Use the profile for this PPPoE secret
                $params['profile'] = $profileName;
            } catch (\Exception $e) {
                Log::warning('Failed to create PPP profile for bandwidth plan', [
                    'plan_id' => $plan->id,
                    'error' => $e->getMessage(),
                ]);
                $progress->updateProgress(3, 'Using default profile due to bandwidth profile creation error', 'in_progress');
                // Continue with default profile
            }
        } else {
            $progress->completeStep('No bandwidth plan specified, using default profile');
        }

        // Step 5: Add PPPoE secret
        $progress->updateProgress(4, "Adding PPPoE secret for user '{$service->username}'...", 'in_progress');

        // Execute the command to add the PPPoE secret
        $result = $device->executeMikrotikCommand($command, $params);

        // Store the MikroTik ID for future reference
        if (isset($result['.id'])) {
            $service->mikrotik_id = $result['.id'];
            $service->save();
        }

        $progress->completeStep("PPPoE secret for '{$service->username}' added successfully");

        // Step 6: Finalize
        $progress->updateProgress(5, 'Finalizing service configuration...', 'in_progress');

        return $result;
    }

    /**
     * Provision a PPPoE service on a MikroTik router.
     */
    protected function provisionPppoeOnMikrotik(PppoeService $service)
    {
        $device = $service->device;

        if (! $this->testDeviceConnection($device)) {
            throw new \Exception('Cannot connect to MikroTik device.');
        }

        // Prepare command to add PPPoE secret
        $command = '/ppp/secret/add';
        $params = [
            'name' => $service->username,
            'password' => $service->password,
            'service' => 'pppoe',
            'profile' => $service->service_profile ?? 'default',
            'comment' => "Customer: {$service->customer->name}, ID: {$service->customer->id}",
        ];

        // Add bandwidth limits if a plan is specified
        if ($service->bandwidthPlan) {
            $plan = $service->bandwidthPlan;

            // If the service profile doesn't exist, create it
            $profileName = "plan-{$plan->id}";
            $profileExists = false;

            try {
                $profiles = $device->executeMikrotikCommand('/ppp/profile/print');
                foreach ($profiles as $profile) {
                    if ($profile['name'] === $profileName) {
                        $profileExists = true;
                        break;
                    }
                }

                if (! $profileExists) {
                    // Create a new profile with bandwidth limits
                    $device->executeMikrotikCommand('/ppp/profile/add', [
                        'name' => $profileName,
                        'rate-limit' => "{$plan->download_speed}k/{$plan->upload_speed}k",
                        'comment' => "Bandwidth Plan: {$plan->name}",
                    ]);
                }

                // Use the profile for this PPPoE secret
                $params['profile'] = $profileName;
            } catch (\Exception $e) {
                Log::warning('Failed to create PPP profile for bandwidth plan', [
                    'plan_id' => $plan->id,
                    'error' => $e->getMessage(),
                ]);
                // Continue with default profile
            }
        }

        // Execute the command to add the PPPoE secret
        $result = $device->executeMikrotikCommand($command, $params);

        // Store the MikroTik ID for future reference
        if (isset($result['.id'])) {
            $service->mikrotik_id = $result['.id'];
            $service->save();
        }

        return $result;
    }

    /**
     * Update a PPPoE service on a MikroTik router.
     */
    protected function updatePppoeOnMikrotik(PppoeService $service)
    {
        $device = $service->device;

        if (! $this->testDeviceConnection($device)) {
            throw new \Exception('Cannot connect to MikroTik device.');
        }

        // Find the PPPoE secret on the router
        $secrets = $device->executeMikrotikCommand('/ppp/secret/print', [
            '?name' => $service->username,
        ]);

        if (empty($secrets)) {
            // Secret doesn't exist, create it
            return $this->provisionPppoeOnMikrotik($service);
        }

        $secret = $secrets[0];
        $secretId = $secret['.id'];

        // Prepare command to update PPPoE secret
        $command = '/ppp/secret/set';
        $params = [
            '.id' => $secretId,
            'password' => $service->password,
            'profile' => $service->service_profile ?? 'default',
            'comment' => "Customer: {$service->customer->name}, ID: {$service->customer->id}",
        ];

        // Add bandwidth limits if a plan is specified
        if ($service->bandwidthPlan) {
            $plan = $service->bandwidthPlan;

            // If the service profile doesn't exist, create it
            $profileName = "plan-{$plan->id}";
            $profileExists = false;

            try {
                $profiles = $device->executeMikrotikCommand('/ppp/profile/print');
                foreach ($profiles as $profile) {
                    if ($profile['name'] === $profileName) {
                        $profileExists = true;
                        break;
                    }
                }

                if (! $profileExists) {
                    // Create a new profile with bandwidth limits
                    $device->executeMikrotikCommand('/ppp/profile/add', [
                        'name' => $profileName,
                        'rate-limit' => "{$plan->download_speed}k/{$plan->upload_speed}k",
                        'comment' => "Bandwidth Plan: {$plan->name}",
                    ]);
                }

                // Use the profile for this PPPoE secret
                $params['profile'] = $profileName;
            } catch (\Exception $e) {
                Log::warning('Failed to create PPP profile for bandwidth plan', [
                    'plan_id' => $plan->id,
                    'error' => $e->getMessage(),
                ]);
                // Continue with default profile
            }
        }

        // Handle service status
        if ($service->status === 'suspended') {
            $params['disabled'] = 'yes';
        } else {
            $params['disabled'] = 'no';
        }

        // Execute the command to update the PPPoE secret
        $result = $device->executeMikrotikCommand($command, $params);

        // Store the MikroTik ID for future reference
        if (! $service->mikrotik_id) {
            $service->mikrotik_id = $secretId;
            $service->save();
        }

        return $result;
    }

    /**
     * Remove a PPPoE service from a MikroTik router.
     */
    protected function removePppoeFromMikrotik(PppoeService $service)
    {
        $device = $service->device;

        if (! $this->testDeviceConnection($device)) {
            throw new \Exception('Cannot connect to MikroTik device.');
        }

        // Find the PPPoE secret on the router
        $secrets = $device->executeMikrotikCommand('/ppp/secret/print', [
            '?name' => $service->username,
        ]);

        if (empty($secrets)) {
            // Secret doesn't exist, nothing to do
            return true;
        }

        $secret = $secrets[0];
        $secretId = $secret['.id'];

        // Execute the command to remove the PPPoE secret
        $result = $device->executeMikrotikCommand('/ppp/secret/remove', [
            '.id' => $secretId,
        ]);

        return $result;
    }

    /**
     * Get active session information for a PPPoE service.
     */
    protected function getActiveSession(PppoeService $service)
    {
        $device = $service->device;

        if (! $this->testDeviceConnection($device)) {
            throw new \Exception('Cannot connect to MikroTik device.');
        }

        // Find active PPPoE sessions for this user
        $activeSessions = $device->executeMikrotikCommand('/ppp/active/print', [
            '?name' => $service->username,
        ]);

        if (empty($activeSessions)) {
            return null;
        }

        $session = $activeSessions[0];

        // Update service with current IP address if available
        if (isset($session['address']) && $service->ip_address !== $session['address']) {
            $service->ip_address = $session['address'];
            $service->save();
        }

        return $session;
    }

    /**
     * Disconnect an active PPPoE session.
     */
    public function disconnect(PppoeService $service)
    {
        try {
            $device = $service->device;

            if (! $this->testDeviceConnection($device)) {
                throw new \Exception('Cannot connect to MikroTik device.');
            }

            // Find active PPPoE sessions for this user
            $activeSessions = $device->executeMikrotikCommand('/ppp/active/print', [
                '?name' => $service->username,
            ]);

            if (empty($activeSessions)) {
                return redirect()->route('services.pppoe.show', $service->id)
                    ->with('info', 'No active session to disconnect.');
            }

            $session = $activeSessions[0];
            $sessionId = $session['.id'];

            // Execute the command to remove the active session
            $device->executeMikrotikCommand('/ppp/active/remove', [
                '.id' => $sessionId,
            ]);

            $this->logActivity('PPPoE session disconnected', [
                'service_id' => $service->id,
                'username' => $service->username,
            ]);

            return redirect()->route('services.pppoe.show', $service->id)
                ->with('success', 'PPPoE session disconnected successfully.');
        } catch (\Exception $e) {
            Log::error('Failed to disconnect PPPoE session', [
                'service_id' => $service->id,
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('services.pppoe.show', $service->id)
                ->with('error', 'Failed to disconnect PPPoE session: '.$e->getMessage());
        }
    }

    /**
     * Provision PPPoE service on MikroTik device synchronously.
     */
    protected function provisionPppoeServiceOnMikrotik(PppoeService $service): array
    {
        try {
            $device = $service->device;

            // Test connection to the device
            if (! $device->testConnection()) {
                return [
                    'success' => false,
                    'error' => 'Cannot connect to MikroTik device',
                ];
            }

            $results = [];

            // Create PPP profile if bandwidth plan exists
            if ($service->bandwidthPlan) {
                $profileResult = $this->createPppProfile($device, $service->bandwidthPlan);
                if (! $profileResult['success']) {
                    return $profileResult;
                }
                $results['profile_name'] = $profileResult['profile_name'];
            }

            // Create PPPoE secret
            $secretResult = $this->createPppoeSecret($device, $service);
            if (! $secretResult['success']) {
                return $secretResult;
            }

            return [
                'success' => true,
                'mikrotik_id' => $secretResult['secret_id'],
                'results' => $results,
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create PPP profile for bandwidth plan.
     */
    protected function createPppProfile(NetworkDevice $device, BandwidthPlan $plan): array
    {
        try {
            $profileName = "plan-{$plan->id}";

            // Check if profile already exists
            $profiles = $device->executeMikrotikCommand('/ppp/profile/print', [
                '?name' => $profileName,
            ]);

            if (empty($profiles)) {
                // Create new profile with bandwidth limits
                $device->executeMikrotikCommand('/ppp/profile/add', [
                    'name' => $profileName,
                    'rate-limit' => "{$plan->upload_speed}M/{$plan->download_speed}M",
                    'comment' => "Bandwidth Plan: {$plan->name}",
                ]);

                Log::info('Created PPP profile for bandwidth plan', [
                    'profile_name' => $profileName,
                    'bandwidth_plan' => $plan->name,
                    'limits' => "{$plan->upload_speed}M/{$plan->download_speed}M",
                ]);
            }

            return [
                'success' => true,
                'profile_name' => $profileName,
            ];

        } catch (\Exception $e) {
            Log::error('Failed to create PPP profile for bandwidth plan', [
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Failed to create PPP profile: '.$e->getMessage(),
            ];
        }
    }

    /**
     * Create PPPoE secret.
     */
    protected function createPppoeSecret(NetworkDevice $device, PppoeService $service): array
    {
        try {
            $profileName = 'default';

            // Use bandwidth plan profile if available
            if ($service->bandwidthPlan) {
                $profileName = "plan-{$service->bandwidthPlan->id}";
            }

            // Check if username already exists
            $existingSecrets = $device->executeMikrotikCommand('/ppp/secret/print', [
                '?name' => $service->username,
            ]);

            if (! empty($existingSecrets)) {
                throw new \Exception("PPPoE username '{$service->username}' already exists on MikroTik device");
            }

            Log::info('Creating PPPoE secret', [
                'service_id' => $service->id,
                'username' => $service->username,
                'profile' => $service->service_profile ?: $profileName,
                'device' => $device->name,
            ]);

            // Create the PPPoE secret
            $addResult = $device->executeMikrotikCommand('/ppp/secret/add', [
                'name' => $service->username,
                'password' => $service->password,
                'service' => 'pppoe',
                'profile' => $service->service_profile ?: $profileName,
                'comment' => "Customer: {$service->customer->name}, ID: {$service->customer->id}",
            ]);

            Log::info('MikroTik add command result', [
                'service_id' => $service->id,
                'add_result' => $addResult,
            ]);

            // Wait a moment for the secret to be created
            sleep(1);

            // Get the created secret to retrieve its ID
            $secrets = $device->executeMikrotikCommand('/ppp/secret/print', [
                '?name' => $service->username,
            ]);

            Log::info('MikroTik secret search result', [
                'service_id' => $service->id,
                'username' => $service->username,
                'secrets_found' => count($secrets),
                'secrets' => $secrets,
            ]);

            if (empty($secrets)) {
                // Try to get all secrets to see what's there
                $allSecrets = $device->executeMikrotikCommand('/ppp/secret/print');
                Log::error('PPPoE secret not found after creation', [
                    'service_id' => $service->id,
                    'username' => $service->username,
                    'total_secrets' => count($allSecrets),
                    'add_result' => $addResult,
                ]);
                throw new \Exception("PPPoE secret '{$service->username}' was not created on MikroTik device");
            }

            $secret = $secrets[0];
            $secretId = $secret['.id'];

            Log::info('Created PPPoE secret successfully', [
                'service_id' => $service->id,
                'secret_id' => $secretId,
                'username' => $service->username,
                'profile' => $service->service_profile ?: $profileName,
                'secret_details' => $secret,
            ]);

            return [
                'success' => true,
                'secret_id' => $secretId,
            ];

        } catch (\Exception $e) {
            Log::error('Failed to create PPPoE secret', [
                'service_id' => $service->id,
                'username' => $service->username,
                'device' => $device->name,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => 'Failed to create PPPoE secret: '.$e->getMessage(),
            ];
        }
    }

    /**
     * Update subscription status when service status changes manually.
     */
    protected function updateSubscriptionStatus(PppoeService $service, string $status): void
    {
        if (! $service->subscription_id) {
            Log::info('Service has no subscription, skipping subscription status update', [
                'service_id' => $service->id,
                'service_status' => $status,
            ]);

            return;
        }

        try {
            $subscription = $service->subscription;

            if (! $subscription) {
                Log::warning('Subscription not found for service', [
                    'service_id' => $service->id,
                    'subscription_id' => $service->subscription_id,
                ]);

                return;
            }

            $oldStatus = $subscription->status;

            // Only update if status is actually changing
            if ($oldStatus === $status) {
                Log::info('Subscription status already matches service status', [
                    'service_id' => $service->id,
                    'subscription_id' => $subscription->id,
                    'status' => $status,
                ]);

                return;
            }

            // Update subscription status and timestamps
            $subscription->status = $status;

            if ($status === 'suspended') {
                $subscription->suspension_date = now();
                $subscription->reactivation_date = null;
            } elseif ($status === 'active') {
                $subscription->reactivation_date = now();
            }

            $subscription->save();

            Log::info('Subscription status updated to match service status', [
                'service_id' => $service->id,
                'subscription_id' => $subscription->id,
                'old_status' => $oldStatus,
                'new_status' => $status,
                'customer_name' => $service->customer->name,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update subscription status', [
                'service_id' => $service->id,
                'subscription_id' => $service->subscription_id,
                'target_status' => $status,
                'error' => $e->getMessage(),
            ]);
            // Don't throw - service status change should still succeed
        }
    }
}
