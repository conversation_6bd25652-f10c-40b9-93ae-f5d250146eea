<?php

namespace App\Http\Controllers\Network;

use App\Http\Controllers\Controller;
use App\Models\Network\NetworkDevice;
use App\Models\Network\NetworkSite;
use Illuminate\Http\Request;

class NetworkSiteController extends Controller
{
    /**
     * Display a listing of the sites.
     */
    public function index(Request $request)
    {
        $query = NetworkSite::query();

        // Apply filters
        if ($request->has('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%'.$request->search.'%')
                    ->orWhere('description', 'like', '%'.$request->search.'%')
                    ->orWhere('address', 'like', '%'.$request->search.'%');
            });
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);

        // Get all sites for Inertia
        $sites = $query->get();

        return \Inertia\Inertia::render('network/sites/index', [
            'sites' => $sites,
        ]);
    }

    /**
     * Show the form for creating a new site.
     */
    public function create()
    {
        return \Inertia\Inertia::render('network/sites/create');
    }

    /**
     * Store a newly created site.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'address' => 'nullable|string|max:255',
            'status' => 'required|string|in:active,inactive,maintenance',
        ]);

        $site = NetworkSite::create($validated);

        // Redirect to sites index with success message
        return redirect()->route('network.sites.index')->with('success', 'Site created successfully.');
    }

    /**
     * Display the specified site.
     */
    public function show(NetworkSite $site)
    {
        $site->load('devices');

        return \Inertia\Inertia::render('network/sites/show', [
            'site' => $site,
        ]);
    }

    /**
     * Show the form for editing the specified site.
     */
    public function edit(NetworkSite $site)
    {
        return \Inertia\Inertia::render('network/sites/edit', [
            'site' => $site,
        ]);
    }

    /**
     * Update the specified site.
     */
    public function update(Request $request, NetworkSite $site)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'address' => 'nullable|string|max:255',
            'status' => 'required|string|in:active,inactive,maintenance',
        ]);

        $site->update($validated);

        return redirect()->route('network.sites.show', $site)->with('success', 'Site updated successfully.');
    }

    /**
     * Remove the specified site.
     */
    public function destroy(NetworkSite $site)
    {
        // Check if the site has children
        $childrenCount = $site->children()->count();
        if ($childrenCount > 0) {
            return response()->json([
                'message' => 'Cannot delete site with children. There are '.$childrenCount.' child sites.',
            ], 422);
        }

        // Check if the site has devices
        $devicesCount = $site->devices()->count();
        if ($devicesCount > 0) {
            return response()->json([
                'message' => 'Cannot delete site with devices. There are '.$devicesCount.' devices at this site.',
            ], 422);
        }

        $site->delete();

        return response()->json(null, 204);
    }

    // Removed children/descendants methods since hierarchical structure was simplified

    /**
     * Get the devices at the specified site.
     */
    public function devices(NetworkSite $site, Request $request)
    {
        $query = $site->devices();

        // Apply filters
        if ($request->has('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%'.$request->search.'%')
                    ->orWhere('type', 'like', '%'.$request->search.'%')
                    ->orWhere('ip_address', 'like', '%'.$request->search.'%');
            });
        }

        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);

        $devices = $query->paginate($request->get('per_page', 15));

        return response()->json($devices);
    }

    /**
     * Get the maps for the specified site.
     */
    public function maps(NetworkSite $site, Request $request)
    {
        $query = $site->maps();

        // Apply filters
        if ($request->has('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%'.$request->search.'%')
                    ->orWhere('description', 'like', '%'.$request->search.'%');
            });
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);

        $maps = $query->paginate($request->get('per_page', 15));

        return response()->json($maps);
    }

    /**
     * Get site statistics.
     */
    public function statistics()
    {
        $totalSites = NetworkSite::count();
        $activeSites = NetworkSite::where('status', 'active')->count();

        $sitesByStatus = NetworkSite::select('status')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('status')
            ->orderByRaw('COUNT(*) DESC')
            ->get();

        $sitesWithMostDevices = NetworkSite::withCount('devices')
            ->orderBy('devices_count', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'total_sites' => $totalSites,
            'active_sites' => $activeSites,
            'sites_by_status' => $sitesByStatus,
            'sites_with_most_devices' => $sitesWithMostDevices,
        ]);
    }

    /**
     * Move devices from one site to another.
     */
    public function moveDevices(Request $request, NetworkSite $site)
    {
        $validated = $request->validate([
            'target_site_id' => 'required|exists:network_sites,id',
            'device_ids' => 'required|array',
            'device_ids.*' => 'required|exists:network_devices,id',
        ]);

        $targetSite = NetworkSite::findOrFail($validated['target_site_id']);

        // Check if devices belong to the source site
        $devicesCount = NetworkDevice::whereIn('id', $validated['device_ids'])
            ->where('site_id', $site->id)
            ->count();

        if ($devicesCount != count($validated['device_ids'])) {
            return response()->json([
                'message' => 'Some devices do not belong to the source site',
            ], 422);
        }

        // Move devices to target site
        NetworkDevice::whereIn('id', $validated['device_ids'])->update([
            'site_id' => $targetSite->id,
        ]);

        return response()->json([
            'message' => $devicesCount.' devices moved successfully',
        ]);
    }
}
