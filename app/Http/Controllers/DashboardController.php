<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Invoice;
use App\Models\Network\NetworkDevice;
use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use App\Services\CurrencyService;
use App\Services\QueryOptimizationService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    /**
     * Display the main dashboard with real data (optimized for performance).
     */
    public function index(Request $request)
    {
        // Use optimized queries with caching
        $optimizedStats = QueryOptimizationService::getDashboardStats();
        $recentActivities = QueryOptimizationService::getRecentActivities(15);
        $deviceUtilization = QueryOptimizationService::getDeviceUtilizationStats();
        $billingSummary = QueryOptimizationService::getBillingSummary();

        // Transform the data to match frontend expectations
        $stats = [
            'totalCustomers' => $optimizedStats['customers']['total'],
            'activeServices' => $optimizedStats['services']['active_static_ip'] + $optimizedStats['services']['active_pppoe'],
            'totalRevenue' => $billingSummary['this_month']['total_amount'] ?? 0,
            'networkUptime' => $this->calculateNetworkUptime($optimizedStats['devices']),
            'customerGrowth' => $this->calculateCustomerGrowth($optimizedStats['customers']['total']),
            'revenueGrowth' => $this->calculateRevenueGrowth($billingSummary),
            'serviceGrowth' => $this->calculateServiceGrowth($optimizedStats['services']),
            'uptimeChange' => '+0.1%', // Mock for now
            'breakdown' => [
                'static_ip_services' => $optimizedStats['services']['total_static_ip'],
                'pppoe_services' => $optimizedStats['services']['total_pppoe'],
                'active_customers' => $optimizedStats['customers']['active'],
                'total_devices' => $optimizedStats['devices']['total'],
                'active_devices' => $optimizedStats['devices']['active'],
            ],
        ];

        // Transform recent activities to match frontend format
        $transformedActivities = collect($recentActivities)->map(function ($activity) {
            return [
                'id' => $activity['id'],
                'customer' => $this->extractCustomerName($activity['description']),
                'action' => $this->extractAction($activity['description']),
                'service' => $activity['type'] === 'static_ip_service' ? 'Static IP' : 'PPPoE',
                'time' => $this->formatTimeForFrontend($activity['created_at']),
                'status' => $activity['status'],
            ];
        })->toArray();

        // Transform network devices for frontend
        $networkDevices = collect($deviceUtilization)->take(5)->map(function ($device) {
            return [
                'id' => $device['device']['id'],
                'name' => $device['device']['name'],
                'location' => $device['device']['ip_address'], // Using IP as location for now
                'status' => $device['services']['active'] > 0 ? 'online' : 'offline',
                'uptime' => $device['services']['utilization_percentage'].'% utilized',
            ];
        })->toArray();

        return Inertia::render('dashboard', [
            'stats' => $stats,
            'recentActivities' => $transformedActivities,
            'networkDevices' => $networkDevices,
            'currencyConfig' => CurrencyService::getJavaScriptConfig(),
        ]);
    }

    /**
     * Calculate network uptime percentage
     */
    protected function calculateNetworkUptime(array $deviceStats): float
    {
        $total = $deviceStats['total'];
        $active = $deviceStats['active'];

        return $total > 0 ? round(($active / $total) * 100, 1) : 100.0;
    }

    /**
     * Calculate customer growth percentage
     */
    protected function calculateCustomerGrowth(int $totalCustomers): string
    {
        // Mock calculation - in real implementation, compare with previous period
        $growth = rand(1, 15);

        return "+{$growth}%";
    }

    /**
     * Calculate revenue growth percentage
     */
    protected function calculateRevenueGrowth(array $billingSummary): string
    {
        $thisMonth = $billingSummary['this_month']['total_amount'] ?? 0;
        $lastMonth = $billingSummary['last_month']['total_amount'] ?? 0;

        if ($lastMonth == 0) {
            return $thisMonth > 0 ? '+100%' : '0%';
        }

        $growth = (($thisMonth - $lastMonth) / $lastMonth) * 100;
        $sign = $growth >= 0 ? '+' : '';

        return $sign.round($growth, 1).'%';
    }

    /**
     * Calculate service growth percentage
     */
    protected function calculateServiceGrowth(array $serviceStats): string
    {
        // Mock calculation - in real implementation, compare with previous period
        $growth = rand(5, 20);

        return "+{$growth}%";
    }

    /**
     * Extract customer name from activity description
     */
    protected function extractCustomerName(string $description): string
    {
        if (preg_match('/for (.+?) \(/', $description, $matches)) {
            return $matches[1];
        }

        return 'Unknown Customer';
    }

    /**
     * Extract action from activity description
     */
    protected function extractAction(string $description): string
    {
        if (strpos($description, 'Static IP service') !== false) {
            return 'Service Created';
        } elseif (strpos($description, 'PPPoE service') !== false) {
            return 'Service Created';
        }

        return 'Activity';
    }

    /**
     * Format time for frontend display
     */
    protected function formatTimeForFrontend(string $isoTime): string
    {
        return \Carbon\Carbon::parse($isoTime)->diffForHumans();
    }

    /**
     * Get comprehensive dashboard statistics.
     */
    protected function getDashboardStats(): array
    {
        // Customer statistics
        $totalCustomers = Customer::count();
        $activeCustomers = Customer::where('status', 'active')->count();
        $previousMonthCustomers = Customer::where('created_at', '<', now()->startOfMonth())->count();
        $customerGrowth = $this->calculateGrowthPercentage($totalCustomers, $previousMonthCustomers);

        // Service statistics
        $staticIpServices = StaticIpService::count();
        $pppoeServices = PppoeService::count();
        $totalServices = $staticIpServices + $pppoeServices;
        $activeServices = StaticIpService::where('status', 'active')->count() +
                         PppoeService::where('status', 'active')->count();

        $previousMonthServices = StaticIpService::where('created_at', '<', now()->startOfMonth())->count() +
                                PppoeService::where('created_at', '<', now()->startOfMonth())->count();
        $serviceGrowth = $this->calculateGrowthPercentage($totalServices, $previousMonthServices);

        // Revenue statistics (from invoices)
        $currentMonthRevenue = Invoice::where('status', 'paid')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('total_amount');

        $previousMonthRevenue = Invoice::where('status', 'paid')
            ->whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->sum('total_amount');

        $revenueGrowth = $this->calculateGrowthPercentage($currentMonthRevenue, $previousMonthRevenue);

        // Network uptime (based on active devices)
        $totalDevices = NetworkDevice::count();
        $activeDevices = NetworkDevice::where('status', 'active')->count();
        $networkUptime = $totalDevices > 0 ? round(($activeDevices / $totalDevices) * 100, 1) : 100;

        // Calculate uptime change (mock for now - would need historical data)
        $uptimeChange = $networkUptime >= 99 ? '+0.1%' : '-0.2%';

        return [
            'totalCustomers' => $totalCustomers,
            'activeServices' => $activeServices,
            'totalRevenue' => $currentMonthRevenue,
            'networkUptime' => $networkUptime,
            'customerGrowth' => $customerGrowth,
            'revenueGrowth' => $revenueGrowth,
            'serviceGrowth' => $serviceGrowth,
            'uptimeChange' => $uptimeChange,
            'breakdown' => [
                'static_ip_services' => $staticIpServices,
                'pppoe_services' => $pppoeServices,
                'active_customers' => $activeCustomers,
                'total_devices' => $totalDevices,
                'active_devices' => $activeDevices,
            ],
        ];
    }

    /**
     * Get recent activities from various system events.
     */
    protected function getRecentActivities(): array
    {
        $activities = [];

        // Recent service activations (last 24 hours)
        $recentStaticIpServices = StaticIpService::with('customer')
            ->where('created_at', '>=', now()->subDay())
            ->where('status', 'active')
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get();

        foreach ($recentStaticIpServices as $service) {
            $activities[] = [
                'id' => 'static_ip_'.$service->id,
                'customer' => $service->customer->name,
                'action' => 'Service Activated',
                'service' => 'Static IP',
                'time' => $service->created_at->diffForHumans(),
                'status' => 'success',
            ];
        }

        // Recent PPPoE service activations
        $recentPppoeServices = PppoeService::with('customer')
            ->where('created_at', '>=', now()->subDay())
            ->where('status', 'active')
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get();

        foreach ($recentPppoeServices as $service) {
            $activities[] = [
                'id' => 'pppoe_'.$service->id,
                'customer' => $service->customer->name,
                'action' => 'Service Activated',
                'service' => 'PPPoE',
                'time' => $service->created_at->diffForHumans(),
                'status' => 'success',
            ];
        }

        // Recent payments (last 24 hours)
        $recentPayments = Invoice::with('customer')
            ->where('status', 'paid')
            ->where('updated_at', '>=', now()->subDay())
            ->orderBy('updated_at', 'desc')
            ->limit(3)
            ->get();

        foreach ($recentPayments as $invoice) {
            $activities[] = [
                'id' => 'payment_'.$invoice->id,
                'customer' => $invoice->customer->name,
                'action' => 'Payment Received',
                'service' => formatCurrency($invoice->total_amount, $invoice->currency ?? null),
                'time' => $invoice->updated_at->diffForHumans(),
                'status' => 'success',
            ];
        }

        // Recent service suspensions
        $suspendedServices = collect();

        $suspendedStaticIp = StaticIpService::with('customer')
            ->where('status', 'suspended')
            ->where('updated_at', '>=', now()->subDay())
            ->orderBy('updated_at', 'desc')
            ->limit(2)
            ->get();

        $suspendedPppoe = PppoeService::with('customer')
            ->where('status', 'suspended')
            ->where('updated_at', '>=', now()->subDay())
            ->orderBy('updated_at', 'desc')
            ->limit(2)
            ->get();

        foreach ($suspendedStaticIp as $service) {
            $activities[] = [
                'id' => 'suspended_static_'.$service->id,
                'customer' => $service->customer->name,
                'action' => 'Service Suspended',
                'service' => 'Static IP',
                'time' => $service->updated_at->diffForHumans(),
                'status' => 'warning',
            ];
        }

        foreach ($suspendedPppoe as $service) {
            $activities[] = [
                'id' => 'suspended_pppoe_'.$service->id,
                'customer' => $service->customer->name,
                'action' => 'Service Suspended',
                'service' => 'PPPoE',
                'time' => $service->updated_at->diffForHumans(),
                'status' => 'warning',
            ];
        }

        // Recent new customers
        $newCustomers = Customer::where('created_at', '>=', now()->subDay())
            ->orderBy('created_at', 'desc')
            ->limit(2)
            ->get();

        foreach ($newCustomers as $customer) {
            $activities[] = [
                'id' => 'customer_'.$customer->id,
                'customer' => $customer->name,
                'action' => 'New Customer',
                'service' => 'Registration',
                'time' => $customer->created_at->diffForHumans(),
                'status' => 'info',
            ];
        }

        // Sort by most recent and limit to 5
        usort($activities, function ($a, $b) {
            return strtotime($b['time']) - strtotime($a['time']);
        });

        return array_slice($activities, 0, 5);
    }

    /**
     * Get network devices status for dashboard (optimized for performance).
     */
    protected function getNetworkDevicesStatus(): array
    {
        $devices = NetworkDevice::with('site')
            ->orderBy('name')
            ->limit(4)
            ->get();

        return $devices->map(function ($device) {
            // Use database status instead of live API calls
            $isOnline = $device->status === 'active';

            // Calculate uptime based on last_connected_at timestamp
            $uptime = '0%';
            if ($device->last_connected_at) {
                $hoursAgo = $device->last_connected_at->diffInHours(now());
                if ($hoursAgo <= 1) {
                    // Recently connected (within last hour) - high uptime
                    $uptime = '99.'.rand(5, 9).'%';
                } elseif ($hoursAgo <= 24) {
                    // Connected within last day - good uptime
                    $uptime = '98.'.rand(0, 9).'%';
                } elseif ($hoursAgo <= 168) { // 7 days
                    // Connected within last week - moderate uptime
                    $uptime = '95.'.rand(0, 9).'%';
                } else {
                    // Not connected recently - low uptime
                    $uptime = rand(80, 94).'.'.rand(0, 9).'%';
                }
            }

            return [
                'id' => $device->id,
                'name' => $device->name,
                'location' => $device->site ? $device->site->name : 'Unknown Location',
                'status' => $isOnline ? 'online' : 'offline',
                'uptime' => $uptime,
            ];
        })->toArray();
    }

    /**
     * Calculate growth percentage between current and previous values.
     */
    protected function calculateGrowthPercentage($current, $previous): string
    {
        if ($previous == 0) {
            return $current > 0 ? '+100%' : '0%';
        }

        $growth = (($current - $previous) / $previous) * 100;
        $sign = $growth >= 0 ? '+' : '';

        return $sign.number_format($growth, 1).'%';
    }
}
