<?php

namespace App\Jobs;

use App\Models\Services\PppoeService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdatePppoeSessionCache implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $service;

    /**
     * Create a new job instance.
     */
    public function __construct(PppoeService $service)
    {
        $this->service = $service;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $device = $this->service->device;

            if (!$device) {
                Log::warning('PPPoE service has no device assigned', [
                    'service_id' => $this->service->id,
                ]);
                return;
            }

            // Test device connection first
            if (!$this->testDeviceConnection($device)) {
                Log::warning('Cannot connect to MikroTik device for session cache update', [
                    'service_id' => $this->service->id,
                    'device_id' => $device->id,
                ]);
                return;
            }

            // Find active PPPoE sessions for this user
            $activeSessions = $device->executeMikrotikCommand('/ppp/active/print', [
                '?name' => $this->service->username,
            ]);

            $sessionData = null;
            if (!empty($activeSessions)) {
                $sessionData = $activeSessions[0];

                // Update service with current IP address if available
                if (isset($sessionData['address']) && $this->service->ip_address !== $sessionData['address']) {
                    $this->service->ip_address = $sessionData['address'];
                    $this->service->save();
                }
            }

            // Update cache
            $this->service->updateSessionCache($sessionData);

            Log::info('PPPoE session cache updated successfully', [
                'service_id' => $this->service->id,
                'is_connected' => $sessionData !== null,
                'ip_address' => $sessionData['address'] ?? null,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update PPPoE session cache', [
                'service_id' => $this->service->id,
                'error' => $e->getMessage(),
            ]);

            // Don't fail the job, just log the error
            // This prevents the job from being retried indefinitely
        }
    }

    /**
     * Test connection to a MikroTik device.
     */
    protected function testDeviceConnection($device): bool
    {
        try {
            $device->executeMikrotikCommand('/system/identity/print');
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
