<?php

namespace App\Jobs\MikroTik;

use App\Models\Services\StaticIpService;
use App\Services\MikrotikAddressListService;
use Illuminate\Support\Facades\Log;

class ReactivateStaticIpServiceJob extends BaseMikroTikJob
{
    /**
     * The Static IP service to reactivate.
     */
    protected StaticIpService $service;

    /**
     * The invoice ID that triggered this reactivation (optional).
     */
    protected ?int $invoiceId;

    /**
     * Create a new job instance.
     */
    public function __construct(StaticIpService $service, ?int $invoiceId = null)
    {
        $this->service = $service;
        $this->invoiceId = $invoiceId;

        parent::__construct($service->device, [
            'service_id' => $service->id,
            'customer_id' => $service->customer_id,
            'customer_name' => $service->customer->name,
            'ip_address' => $service->ip_address,
            'mikrotik_id' => $service->mikrotik_id,
            'invoice_id' => $invoiceId,
        ]);
    }

    /**
     * Get the operation type for logging.
     */
    protected function getOperationType(): string
    {
        return 'reactivate_static_ip_service';
    }

    /**
     * Execute the Static IP service reactivation operation.
     */
    protected function executeOperation(): mixed
    {
        // Check if service is already active
        $this->service->refresh();
        if ($this->service->status === 'active') {
            Log::info('Static IP service already active, skipping', [
                'service_id' => $this->service->id,
            ]);

            return ['status' => 'already_active'];
        }

        // Validate service state
        if ($this->service->status !== 'suspended') {
            throw new \Exception("Cannot reactivate service with status: {$this->service->status}");
        }

        // Must have stored MikroTik ID for removal
        if (! $this->service->mikrotik_id) {
            throw new \Exception('Cannot reactivate service - no MikroTik ID stored');
        }

        $addressListService = new MikrotikAddressListService;

        // Reactivate customer (remove from address list)
        $success = $addressListService->reactivateCustomer(
            $this->device,
            $this->service->mikrotik_id,
            $this->service->customer->name
        );

        if (! $success) {
            throw new \Exception('Failed to reactivate customer on MikroTik');
        }

        // Re-enable bandwidth queues
        $this->enableBandwidthQueues();

        // Update service status and clear MikroTik ID
        $this->service->mikrotik_id = null;
        $this->service->status = 'active';
        $this->service->save();

        // Update subscription status to active if this service has a subscription
        $this->updateSubscriptionStatus('active');

        Log::info('Static IP service reactivated successfully', [
            'service_id' => $this->service->id,
            'customer_name' => $this->service->customer->name,
            'ip_address' => $this->service->ip_address,
            'invoice_id' => $this->invoiceId,
        ]);

        return [
            'status' => 'active',
            'invoice_id' => $this->invoiceId,
        ];
    }

    /**
     * Enable bandwidth queues for the reactivated service.
     */
    protected function enableBandwidthQueues(): void
    {
        try {
            $queues = $this->device->executeMikrotikCommand('/queue/simple/print', [
                '?target' => $this->service->ip_address.'/32',
            ]);

            foreach ($queues as $queue) {
                $this->device->executeMikrotikCommand('/queue/simple/set', [
                    '.id' => $queue['.id'],
                    'disabled' => 'no',
                ]);

                Log::info('Enabled bandwidth queue for reactivated service', [
                    'service_id' => $this->service->id,
                    'queue_id' => $queue['.id'],
                    'queue_name' => $queue['name'] ?? 'unnamed',
                ]);
            }
        } catch (\Exception $e) {
            Log::warning('Failed to enable bandwidth queues for reactivated service', [
                'service_id' => $this->service->id,
                'ip_address' => $this->service->ip_address,
                'error' => $e->getMessage(),
            ]);
            // Don't fail the reactivation if queue enabling fails
        }
    }

    /**
     * Update subscription status when service is reactivated.
     */
    protected function updateSubscriptionStatus(string $status): void
    {
        if (! $this->service->subscription_id) {
            Log::info('Service has no subscription, skipping subscription status update', [
                'service_id' => $this->service->id,
                'service_status' => $status,
            ]);

            return;
        }

        try {
            $subscription = $this->service->subscription;

            if (! $subscription) {
                Log::warning('Subscription not found for service', [
                    'service_id' => $this->service->id,
                    'subscription_id' => $this->service->subscription_id,
                ]);

                return;
            }

            $oldStatus = $subscription->status;

            // Only update if status is actually changing
            if ($oldStatus === $status) {
                Log::info('Subscription status already matches service status', [
                    'service_id' => $this->service->id,
                    'subscription_id' => $subscription->id,
                    'status' => $status,
                ]);

                return;
            }

            // Update subscription status and timestamps
            $subscription->status = $status;

            if ($status === 'active') {
                $subscription->reactivation_date = now();
            }

            $subscription->save();

            Log::info('Subscription status updated after service reactivation', [
                'service_id' => $this->service->id,
                'subscription_id' => $subscription->id,
                'old_status' => $oldStatus,
                'new_status' => $status,
                'customer_name' => $this->service->customer->name,
                'invoice_id' => $this->invoiceId,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update subscription status after service reactivation', [
                'service_id' => $this->service->id,
                'subscription_id' => $this->service->subscription_id,
                'target_status' => $status,
                'invoice_id' => $this->invoiceId,
                'error' => $e->getMessage(),
            ]);
            // Don't throw - service reactivation should still succeed
        }
    }

    /**
     * Handle final failure after all retries are exhausted.
     */
    protected function handleFinalFailure(\Throwable $exception): void
    {
        // Mark service as failed reactivation
        $this->service->refresh();
        $this->service->status = 'reactivation_failed';
        $this->service->save();

        Log::critical('Static IP service reactivation permanently failed', [
            'service_id' => $this->service->id,
            'customer_name' => $this->service->customer->name,
            'ip_address' => $this->service->ip_address,
            'mikrotik_id' => $this->service->mikrotik_id,
            'invoice_id' => $this->invoiceId,
            'error' => $exception->getMessage(),
        ]);

        // Could send notification to admin here
    }
}
