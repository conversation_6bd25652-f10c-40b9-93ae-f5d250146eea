<?php

namespace App\Jobs;

use App\Events\StaticIpProvisioningUpdated;
use App\Models\Services\StaticIpService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProvisionStaticIpServiceWithProgress implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $service;
    protected $isReprovisioning;

    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public $timeout = 300; // 5 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(StaticIpService $service, bool $isReprovisioning = false)
    {
        $this->service = $service;
        $this->isReprovisioning = $isReprovisioning;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Start provisioning
            $this->service->startProvisioning('Initializing provisioning...');
            $this->broadcastUpdate('started');

            // Stage 1: Connect to MikroTik device (10%)
            $this->updateProgress('Connecting to MikroTik device...', 10);
            $device = $this->service->device;
            
            if (!$device) {
                throw new \Exception('No network device assigned to this service');
            }

            if (!$this->testDeviceConnection($device)) {
                throw new \Exception('Cannot connect to MikroTik device: ' . $device->name);
            }

            // Stage 2: Validate IP pool availability (25%)
            $this->updateProgress('Validating IP pool availability...', 25);
            $this->validateIpPoolAvailability();

            // Stage 3: Create IP route on router (50%)
            $this->updateProgress('Creating IP route on router...', 50);
            $this->createIpRoute();

            // Stage 4: Configure firewall rules (75%)
            $this->updateProgress('Configuring firewall rules...', 75);
            $this->configureFirewallRules();

            // Stage 5: Set up NAT rules (90%)
            $this->updateProgress('Setting up NAT rules...', 90);
            $this->setupNatRules();

            // Stage 6: Verify configuration (95%)
            $this->updateProgress('Verifying configuration...', 95);
            $this->verifyConfiguration();

            // Stage 7: Complete provisioning (100%)
            $this->updateProgress('Provisioning completed successfully', 100);
            $this->service->completeProvisioning();
            $this->broadcastUpdate('completed');

            Log::info('Static IP service provisioned successfully', [
                'service_id' => $this->service->id,
                'ip_address' => $this->service->ip_address,
                'customer_id' => $this->service->customer_id,
                'attempts' => $this->service->provisioning_attempts,
            ]);

        } catch (\Exception $e) {
            $this->handleProvisioningFailure($e);
        }
    }

    /**
     * Handle job failure.
     */
    public function failed(\Throwable $exception): void
    {
        $this->handleProvisioningFailure($exception);
    }

    /**
     * Update provisioning progress and broadcast.
     */
    protected function updateProgress(string $stage, int $progress): void
    {
        $this->service->updateProvisioningProgress($stage, $progress);
        $this->broadcastUpdate('progress');
    }

    /**
     * Broadcast provisioning update.
     */
    protected function broadcastUpdate(string $eventType, array $data = []): void
    {
        broadcast(new StaticIpProvisioningUpdated($this->service, $eventType, $data));
    }

    /**
     * Test connection to MikroTik device.
     */
    protected function testDeviceConnection($device): bool
    {
        try {
            $device->executeMikrotikCommand('/system/identity/print');
            return true;
        } catch (\Exception $e) {
            Log::error('Device connection test failed', [
                'device_id' => $device->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Validate IP pool availability.
     */
    protected function validateIpPoolAvailability(): void
    {
        if ($this->service->ip_pool_id) {
            $ipPool = $this->service->ipPool;
            if (!$ipPool || $ipPool->status !== 'active') {
                throw new \Exception('IP pool is not available or inactive');
            }

            // Check if IP is still available in the pool
            $availableIps = $ipPool->getAllAvailableIps();
            if (!in_array($this->service->ip_address, $availableIps)) {
                throw new \Exception('IP address is no longer available in the pool');
            }
        }
    }

    /**
     * Create IP route on MikroTik router.
     */
    protected function createIpRoute(): void
    {
        $device = $this->service->device;
        
        // Check if route already exists
        $existingRoutes = $device->executeMikrotikCommand('/ip/route/print', [
            '?dst-address' => $this->service->ip_address . '/32'
        ]);

        if (!empty($existingRoutes)) {
            // Route exists, update it
            $routeId = $existingRoutes[0]['.id'];
            $device->executeMikrotikCommand('/ip/route/set', [
                '.id' => $routeId,
                'gateway' => $this->service->gateway,
                'comment' => "Customer: {$this->service->customer->name}, ID: {$this->service->customer->id}"
            ]);
        } else {
            // Create new route
            $result = $device->executeMikrotikCommand('/ip/route/add', [
                'dst-address' => $this->service->ip_address . '/32',
                'gateway' => $this->service->gateway,
                'comment' => "Customer: {$this->service->customer->name}, ID: {$this->service->customer->id}"
            ]);

            // Store the route ID for future reference
            if (isset($result[0]['.id'])) {
                $this->service->update(['mikrotik_route_id' => $result[0]['.id']]);
            }
        }
    }

    /**
     * Configure firewall rules.
     */
    protected function configureFirewallRules(): void
    {
        $device = $this->service->device;
        
        // Add firewall rule to allow traffic
        $device->executeMikrotikCommand('/ip/firewall/filter/add', [
            'chain' => 'forward',
            'src-address' => $this->service->ip_address . '/32',
            'action' => 'accept',
            'comment' => "Allow traffic from customer {$this->service->customer->name}"
        ]);
    }

    /**
     * Set up NAT rules.
     */
    protected function setupNatRules(): void
    {
        $device = $this->service->device;
        
        // Add NAT rule for masquerading
        $device->executeMikrotikCommand('/ip/firewall/nat/add', [
            'chain' => 'srcnat',
            'src-address' => $this->service->ip_address . '/32',
            'action' => 'masquerade',
            'comment' => "NAT for customer {$this->service->customer->name}"
        ]);
    }

    /**
     * Verify the configuration is working.
     */
    protected function verifyConfiguration(): void
    {
        $device = $this->service->device;
        
        // Verify route exists
        $routes = $device->executeMikrotikCommand('/ip/route/print', [
            '?dst-address' => $this->service->ip_address . '/32'
        ]);

        if (empty($routes)) {
            throw new \Exception('Route verification failed - route not found');
        }
    }

    /**
     * Handle provisioning failure.
     */
    protected function handleProvisioningFailure(\Throwable $exception): void
    {
        $errorMessage = $exception->getMessage();
        
        Log::error('Static IP service provisioning failed', [
            'service_id' => $this->service->id,
            'error' => $errorMessage,
            'attempts' => $this->service->provisioning_attempts,
            'is_reprovisioning' => $this->isReprovisioning,
        ]);

        $this->service->failProvisioning($errorMessage, $this->isReprovisioning);
        $this->broadcastUpdate('failed', ['error' => $errorMessage]);
    }
}
