<?php

namespace App\Console\Commands;

use App\Jobs\UpdatePppoeSessionCache;
use App\Models\Services\PppoeService;
use Illuminate\Console\Command;

class UpdatePppoeSessionCaches extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pppoe:update-session-cache 
                            {--service-id= : Update cache for specific service ID}
                            {--batch-size=50 : Number of services to process in each batch}
                            {--delay=1 : Delay in seconds between batches}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update PPPoE session cache for all or specific services';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $serviceId = $this->option('service-id');
        $batchSize = (int) $this->option('batch-size');
        $delay = (int) $this->option('delay');

        if ($serviceId) {
            // Update specific service
            $service = PppoeService::find($serviceId);
            if (!$service) {
                $this->error("PPPoE service with ID {$serviceId} not found.");
                return 1;
            }

            $this->info("Updating session cache for service ID: {$serviceId}");
            UpdatePppoeSessionCache::dispatch($service);
            $this->info("Job dispatched successfully.");
            return 0;
        }

        // Update all active services
        $query = PppoeService::where('status', 'active')
            ->with('device');

        $totalServices = $query->count();
        $this->info("Found {$totalServices} active PPPoE services to update.");

        if ($totalServices === 0) {
            $this->info("No active PPPoE services found.");
            return 0;
        }

        $processed = 0;
        $batches = 0;

        $query->chunk($batchSize, function ($services) use (&$processed, &$batches, $delay, $totalServices) {
            $batches++;
            $this->info("Processing batch {$batches} ({$services->count()} services)...");

            foreach ($services as $service) {
                UpdatePppoeSessionCache::dispatch($service);
                $processed++;
            }

            $this->info("Batch {$batches} dispatched. Progress: {$processed}/{$totalServices}");

            // Add delay between batches to prevent overwhelming the queue
            if ($delay > 0 && $processed < $totalServices) {
                $this->info("Waiting {$delay} seconds before next batch...");
                sleep($delay);
            }
        });

        $this->info("All {$processed} session cache update jobs have been dispatched.");
        $this->info("Jobs will be processed by the queue workers.");
        
        return 0;
    }
}
