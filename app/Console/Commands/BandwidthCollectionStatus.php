<?php

namespace App\Console\Commands;

use App\Models\Bandwidth\BandwidthUsage;
use App\Models\Network\NetworkDevice;
use Carbon\Carbon;
use Illuminate\Console\Command;

class BandwidthCollectionStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bandwidth:collection-status {--device-id= : Show status for specific device} {--hours=24 : Hours to look back}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Show bandwidth collection status and statistics';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $deviceId = $this->option('device-id');
        $hours = (int) $this->option('hours');

        $this->info("📊 Bandwidth Collection Status (Last {$hours} hours)");
        $this->line('');

        $startTime = now()->subHours($hours);

        // Get devices
        $query = NetworkDevice::where('type', 'mikrotik');
        if ($deviceId) {
            $query->where('id', $deviceId);
        }
        $devices = $query->get();

        if ($devices->isEmpty()) {
            $this->warn('No MikroTik devices found');

            return 1;
        }

        // Overall statistics
        $this->showOverallStatistics($startTime);
        $this->line('');

        // Per-device statistics
        $this->showDeviceStatistics($devices, $startTime);
        $this->line('');

        // Recent collection activity
        $this->showRecentActivity($startTime);

        return 0;
    }

    /**
     * Show overall collection statistics.
     */
    protected function showOverallStatistics(Carbon $startTime): void
    {
        $this->info('🌐 Overall Statistics:');

        // Total usage records
        $totalRecords = BandwidthUsage::where('created_at', '>=', $startTime)->count();
        $this->line("📋 Total usage records: {$totalRecords}");

        // Unique customers with usage
        $uniqueCustomers = BandwidthUsage::where('created_at', '>=', $startTime)
            ->distinct('usageable_id', 'usageable_type')
            ->count();
        $this->line("👥 Customers with usage data: {$uniqueCustomers}");

        // Total bandwidth
        $totalUsage = BandwidthUsage::where('created_at', '>=', $startTime)
            ->selectRaw('SUM(download) as total_download, SUM(upload) as total_upload, SUM(total) as total_bandwidth')
            ->first();

        if ($totalUsage) {
            $this->line('📥 Total download: '.$this->formatBytes($totalUsage->total_download));
            $this->line('📤 Total upload: '.$this->formatBytes($totalUsage->total_upload));
            $this->line('📊 Total bandwidth: '.$this->formatBytes($totalUsage->total_bandwidth));
        }

        // Collection frequency
        $collections = BandwidthUsage::where('created_at', '>=', $startTime)
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d %H:%i") as collection_time')
            ->groupBy('collection_time')
            ->orderBy('collection_time', 'desc')
            ->limit(5)
            ->get();

        if ($collections->isNotEmpty()) {
            $this->line('🕐 Recent collections:');
            foreach ($collections as $collection) {
                $count = BandwidthUsage::where('created_at', 'like', $collection->collection_time.'%')->count();
                $this->line("   {$collection->collection_time}: {$count} records");
            }
        }
    }

    /**
     * Show per-device statistics.
     */
    protected function showDeviceStatistics($devices, Carbon $startTime): void
    {
        $this->info('📡 Per-Device Statistics:');

        foreach ($devices as $device) {
            $this->line("--- {$device->name} ({$device->ip_address}) ---");

            // Count services on this device
            $staticIpCount = $device->staticIpServices()->where('status', 'active')->count();
            $pppoeCount = $device->pppoeServices()->where('status', 'active')->count();
            $totalServices = $staticIpCount + $pppoeCount;

            $this->line("🔧 Services: {$totalServices} total ({$staticIpCount} Static IP, {$pppoeCount} PPPoE)");

            // Usage records for this device's services
            $deviceUsage = BandwidthUsage::where('created_at', '>=', $startTime)
                ->where(function ($query) use ($device) {
                    $query->whereHasMorph('usageable', ['App\Models\Services\StaticIpService'], function ($q) use ($device) {
                        $q->where('device_id', $device->id);
                    })->orWhereHasMorph('usageable', ['App\Models\Services\PppoeService'], function ($q) use ($device) {
                        $q->where('device_id', $device->id);
                    });
                })
                ->selectRaw('COUNT(*) as records, SUM(total) as total_bytes')
                ->first();

            if ($deviceUsage && $deviceUsage->records > 0) {
                $this->line("📊 Usage records: {$deviceUsage->records}");
                $this->line('📈 Total bandwidth: '.$this->formatBytes($deviceUsage->total_bytes));

                // Calculate coverage percentage
                $coverage = $totalServices > 0 ? round(($deviceUsage->records / $totalServices) * 100, 1) : 0;
                $this->line("📋 Collection coverage: {$coverage}%");
            } else {
                $this->line('⚠️  No usage data collected');
            }

            // Last collection time
            $lastCollection = BandwidthUsage::whereHasMorph('usageable', ['App\Models\Services\StaticIpService', 'App\Models\Services\PppoeService'], function ($q) use ($device) {
                $q->where('device_id', $device->id);
            })
                ->orderBy('created_at', 'desc')
                ->first();

            if ($lastCollection) {
                $lastTime = $lastCollection->created_at->diffForHumans();
                $this->line("🕐 Last collection: {$lastTime}");
            } else {
                $this->line('🕐 Last collection: Never');
            }

            $this->line('');
        }
    }

    /**
     * Show recent collection activity.
     */
    protected function showRecentActivity(Carbon $startTime): void
    {
        $this->info('📈 Recent Collection Activity:');

        // Hourly breakdown
        $hourlyStats = BandwidthUsage::where('created_at', '>=', $startTime)
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d %H:00") as hour, COUNT(*) as records, SUM(total) as bytes')
            ->groupBy('hour')
            ->orderBy('hour', 'desc')
            ->limit(12)
            ->get();

        if ($hourlyStats->isNotEmpty()) {
            $this->line('Last 12 hours:');
            foreach ($hourlyStats as $stat) {
                $hour = Carbon::parse($stat->hour)->format('M j, H:i');
                $this->line("   {$hour}: {$stat->records} records, ".$this->formatBytes($stat->bytes));
            }
        } else {
            $this->line('No recent activity found');
        }

        // Check for missing collections (gaps > 20 minutes)
        $this->line('');
        $this->info('🔍 Collection Health Check:');

        $recentCollections = BandwidthUsage::where('created_at', '>=', $startTime)
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d %H:%i") as collection_time')
            ->groupBy('collection_time')
            ->orderBy('collection_time', 'desc')
            ->limit(10)
            ->pluck('collection_time')
            ->toArray();

        if (count($recentCollections) >= 2) {
            $gaps = [];
            for ($i = 0; $i < count($recentCollections) - 1; $i++) {
                $current = Carbon::parse($recentCollections[$i]);
                $next = Carbon::parse($recentCollections[$i + 1]);
                $diffMinutes = $current->diffInMinutes($next);

                if ($diffMinutes > 20) {
                    $gaps[] = "Gap of {$diffMinutes} minutes between {$next->format('H:i')} and {$current->format('H:i')}";
                }
            }

            if (empty($gaps)) {
                $this->line('✅ No significant collection gaps detected');
            } else {
                $this->warn('⚠️  Collection gaps detected:');
                foreach ($gaps as $gap) {
                    $this->line("   {$gap}");
                }
            }
        }

        // Next expected collection
        $lastCollection = BandwidthUsage::orderBy('created_at', 'desc')->first();
        if ($lastCollection) {
            $nextExpected = $lastCollection->created_at->addMinutes(15);
            $this->line("🕐 Next collection expected: {$nextExpected->format('Y-m-d H:i:s')} ({$nextExpected->diffForHumans()})");
        }
    }

    /**
     * Format bytes for human reading.
     */
    protected function formatBytes(int $bytes): string
    {
        if ($bytes >= 1024 * 1024 * 1024 * 1024) {
            return round($bytes / (1024 * 1024 * 1024 * 1024), 2).' TB';
        } elseif ($bytes >= 1024 * 1024 * 1024) {
            return round($bytes / (1024 * 1024 * 1024), 2).' GB';
        } elseif ($bytes >= 1024 * 1024) {
            return round($bytes / (1024 * 1024), 2).' MB';
        } elseif ($bytes >= 1024) {
            return round($bytes / 1024, 2).' KB';
        } else {
            return $bytes.' B';
        }
    }
}
