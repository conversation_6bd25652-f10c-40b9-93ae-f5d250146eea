<?php

namespace App\Console\Commands;

use App\Models\Organization;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class OrganizationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'organization:manage 
                            {action : The action to perform (create, list, assign-user)}
                            {--name= : Organization name}
                            {--description= : Organization description}
                            {--user-email= : User email for assignment}
                            {--user-name= : User name for creation}
                            {--user-password= : User password for creation}
                            {--org-id= : Organization ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage organizations and user assignments';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'create':
                return $this->createOrganization();
            case 'list':
                return $this->listOrganizations();
            case 'assign-user':
                return $this->assignUser();
            case 'create-user':
                return $this->createUser();
            default:
                $this->error("Unknown action: {$action}");
                return 1;
        }
    }

    /**
     * Create a new organization.
     */
    protected function createOrganization()
    {
        $name = $this->option('name') ?: $this->ask('Organization name');
        $description = $this->option('description') ?: $this->ask('Organization description (optional)', '');

        if (!$name) {
            $this->error('Organization name is required');
            return 1;
        }

        $slug = Str::slug($name);

        // Check if organization already exists
        if (Organization::where('slug', $slug)->exists()) {
            $this->error("Organization with slug '{$slug}' already exists");
            return 1;
        }

        $organization = Organization::create([
            'name' => $name,
            'slug' => $slug,
            'description' => $description,
            'status' => 'active',
        ]);

        $this->info("Organization '{$name}' created successfully with ID: {$organization->id}");

        // Ask if user wants to create an admin user
        if ($this->confirm('Create an admin user for this organization?')) {
            $this->createUserForOrganization($organization);
        }

        return 0;
    }

    /**
     * List all organizations.
     */
    protected function listOrganizations()
    {
        $organizations = Organization::with('users')->get();

        if ($organizations->isEmpty()) {
            $this->info('No organizations found');
            return 0;
        }

        $headers = ['ID', 'Name', 'Slug', 'Status', 'Users Count', 'Created At'];
        $rows = [];

        foreach ($organizations as $org) {
            $rows[] = [
                $org->id,
                $org->name,
                $org->slug,
                $org->status,
                $org->users->count(),
                $org->created_at->format('Y-m-d H:i:s'),
            ];
        }

        $this->table($headers, $rows);
        return 0;
    }

    /**
     * Assign an existing user to an organization.
     */
    protected function assignUser()
    {
        $userEmail = $this->option('user-email') ?: $this->ask('User email');
        $orgId = $this->option('org-id') ?: $this->ask('Organization ID');

        if (!$userEmail || !$orgId) {
            $this->error('Both user email and organization ID are required');
            return 1;
        }

        $user = User::where('email', $userEmail)->first();
        if (!$user) {
            $this->error("User with email '{$userEmail}' not found");
            return 1;
        }

        $organization = Organization::find($orgId);
        if (!$organization) {
            $this->error("Organization with ID '{$orgId}' not found");
            return 1;
        }

        $user->organization_id = $organization->id;
        $user->save();

        $this->info("User '{$userEmail}' assigned to organization '{$organization->name}'");
        return 0;
    }

    /**
     * Create a new user for an organization.
     */
    protected function createUser()
    {
        $orgId = $this->option('org-id') ?: $this->ask('Organization ID');
        $name = $this->option('user-name') ?: $this->ask('User name');
        $email = $this->option('user-email') ?: $this->ask('User email');
        $password = $this->option('user-password') ?: $this->secret('User password');

        if (!$orgId || !$name || !$email || !$password) {
            $this->error('All user details are required');
            return 1;
        }

        $organization = Organization::find($orgId);
        if (!$organization) {
            $this->error("Organization with ID '{$orgId}' not found");
            return 1;
        }

        // Check if user already exists
        if (User::where('email', $email)->exists()) {
            $this->error("User with email '{$email}' already exists");
            return 1;
        }

        $user = User::create([
            'name' => $name,
            'email' => $email,
            'password' => Hash::make($password),
            'organization_id' => $organization->id,
            'email_verified_at' => now(),
        ]);

        $this->info("User '{$name}' created successfully for organization '{$organization->name}'");
        return 0;
    }

    /**
     * Create a user for a specific organization.
     */
    protected function createUserForOrganization(Organization $organization)
    {
        $name = $this->ask('Admin user name', $organization->name . ' Admin');
        $email = $this->ask('Admin user email', 'admin@' . $organization->slug . '.com');
        $password = $this->secret('Admin user password');

        if (!$password) {
            $this->error('Password is required');
            return;
        }

        // Check if user already exists
        if (User::where('email', $email)->exists()) {
            $this->error("User with email '{$email}' already exists");
            return;
        }

        $user = User::create([
            'name' => $name,
            'email' => $email,
            'password' => Hash::make($password),
            'organization_id' => $organization->id,
            'email_verified_at' => now(),
        ]);

        $this->info("Admin user '{$name}' created successfully");
    }
}
