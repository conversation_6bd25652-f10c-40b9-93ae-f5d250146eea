<?php

namespace App\Console\Commands;

use App\Models\Network\NetworkDevice;
use App\Models\Services\StaticIpService;
use App\Services\MikrotikAddressListService;
use Illuminate\Console\Command;

class MigrateToAddressListSuspension extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:address-list-suspension
                            {--dry-run : Show what would be done without making changes}
                            {--device= : Specific device ID to migrate}
                            {--all : Migrate all MikroTik devices}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate from individual firewall rules to address list suspension system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $deviceId = $this->option('device');
        $allDevices = $this->option('all');

        if (! $deviceId && ! $allDevices) {
            $this->error('Please specify either --device=ID or --all');

            return 1;
        }

        $this->info('🔄 Starting migration to address list suspension system');
        if ($dryRun) {
            $this->warn('🔍 DRY RUN MODE - No changes will be made');
        }
        $this->newLine();

        $devices = $this->getDevices($deviceId, $allDevices);

        if ($devices->isEmpty()) {
            $this->error('No MikroTik devices found');

            return 1;
        }

        $addressListService = new MikrotikAddressListService;
        $totalMigrated = 0;
        $totalErrors = 0;

        foreach ($devices as $device) {
            $this->info("📡 Processing device: {$device->name} ({$device->ip_address})");

            $result = $this->migrateDevice($device, $addressListService, $dryRun);
            $totalMigrated += $result['migrated'];
            $totalErrors += $result['errors'];
        }

        $this->newLine();
        $this->info('✅ Migration completed!');
        $this->line("   Services migrated: {$totalMigrated}");
        $this->line("   Errors encountered: {$totalErrors}");

        if ($dryRun) {
            $this->warn('🔍 This was a dry run. Run without --dry-run to apply changes.');
        }

        return 0;
    }

    /**
     * Get devices based on options.
     */
    private function getDevices($deviceId, $allDevices)
    {
        if ($deviceId) {
            return NetworkDevice::where('id', $deviceId)
                ->where(function ($query) {
                    $query->where('type', 'mikrotik')->orWhere('type', 'router');
                })
                ->get();
        }

        return NetworkDevice::where('type', 'mikrotik')
            ->orWhere('type', 'router')
            ->get();
    }

    /**
     * Migrate a single device.
     */
    private function migrateDevice(NetworkDevice $device, MikrotikAddressListService $service, bool $dryRun): array
    {
        $migrated = 0;
        $errors = 0;

        try {
            // Step 1: Setup address list infrastructure
            $this->line('  🔧 Setting up address list infrastructure...');

            if (! $dryRun) {
                if (! $service->ensureAddressListExists($device)) {
                    throw new \Exception('Failed to create address list');
                }

                if (! $service->ensureMasterFirewallRuleExists($device)) {
                    throw new \Exception('Failed to create master firewall rule');
                }
            }

            $this->line('     ✓ Address list and master rule ready');

            // Step 2: Find suspended static IP services
            $suspendedServices = StaticIpService::where('device_id', $device->id)
                ->where('status', 'suspended')
                ->with('customer')
                ->get();

            $this->line("  📋 Found {$suspendedServices->count()} suspended static IP services");

            // Step 3: Find legacy firewall rules
            $legacyRules = [];
            if (! $dryRun) {
                $legacyRules = $device->executeMikrotikCommand('/ip/firewall/filter/print', [
                    '?chain' => 'forward',
                    '?action' => 'drop',
                    '?comment' => 'SUSPENDED:*',
                ]);
            }

            $this->line('  🗑️  Found '.count($legacyRules).' legacy firewall rules to clean up');

            // Step 4: Migrate each suspended service
            foreach ($suspendedServices as $suspendedService) {
                $this->line("     🔄 Migrating: {$suspendedService->customer->name} ({$suspendedService->ip_address})");

                if (! $dryRun) {
                    $success = $service->addIpToSuspendedList(
                        $device,
                        $suspendedService->ip_address,
                        $suspendedService->customer->name
                    );

                    if ($success) {
                        $migrated++;
                        $this->line('        ✓ Added to address list');
                    } else {
                        $errors++;
                        $this->line('        ✗ Failed to add to address list');
                    }
                } else {
                    $migrated++;
                    $this->line('        ✓ Would add to address list');
                }
            }

            // Step 5: Clean up legacy rules
            if (! $dryRun) {
                foreach ($legacyRules as $rule) {
                    try {
                        $device->executeMikrotikCommand('/ip/firewall/filter/remove', [
                            '.id' => $rule['.id'],
                        ]);
                        $this->line("     🗑️  Removed legacy rule: {$rule['comment']}");
                    } catch (\Exception $e) {
                        $errors++;
                        $this->line("     ✗ Failed to remove rule: {$rule['comment']}");
                    }
                }
            } else {
                foreach ($legacyRules as $rule) {
                    $this->line("     🗑️  Would remove legacy rule: {$rule['comment']}");
                }
            }

        } catch (\Exception $e) {
            $this->error('  ✗ Device migration failed: '.$e->getMessage());
            $errors++;
        }

        return ['migrated' => $migrated, 'errors' => $errors];
    }
}
