<?php

namespace App\Console\Commands;

use App\Jobs\CollectDeviceBandwidthUsage;
use App\Models\Network\NetworkDevice;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;

class CollectBandwidthUsage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bandwidth:collect-usage {--device-id= : Collect from specific device only} {--dry-run : Show what would be collected without storing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Collect bandwidth usage data from MikroTik devices via SNMP and API';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $deviceId = $this->option('device-id');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('DRY RUN MODE - No data will be stored');
        }

        $this->info('🚀 Starting bandwidth usage collection...');

        // Get MikroTik devices to collect from
        $query = NetworkDevice::where('type', 'mikrotik')
            ->where('status', 'active');

        if ($deviceId) {
            $query->where('id', $deviceId);
        }

        $devices = $query->get();

        if ($devices->isEmpty()) {
            $this->warn('No active MikroTik devices found');

            return 1;
        }

        $this->info("Found {$devices->count()} MikroTik devices to collect from");

        $startTime = now();
        $successCount = 0;
        $failureCount = 0;

        // Process devices in parallel using jobs
        $jobs = [];
        foreach ($devices as $device) {
            $this->line("📡 Queuing collection job for: {$device->name} ({$device->ip_address})");

            if ($dryRun) {
                // For dry run, execute synchronously to show results
                try {
                    $job = new CollectDeviceBandwidthUsage($device, $dryRun);
                    $result = $job->handle();

                    if ($result['success']) {
                        $this->info("  ✅ {$device->name}: {$result['customers_processed']} customers, {$result['interfaces_processed']} interfaces");
                        $successCount++;
                    } else {
                        $this->error("  ❌ {$device->name}: {$result['error']}");
                        $failureCount++;
                    }
                } catch (\Exception $e) {
                    $this->error("  ❌ {$device->name}: ".$e->getMessage());
                    $failureCount++;
                }
            } else {
                // Queue job for background processing
                $job = new CollectDeviceBandwidthUsage($device, $dryRun);
                Queue::push($job);
                $jobs[] = $job;
            }
        }

        if (! $dryRun) {
            $this->info("📋 Queued {$devices->count()} collection jobs for background processing");
            $this->info('💡 Monitor job progress with: php artisan queue:work');
            $this->info('📊 Check collection status with: php artisan bandwidth:collection-status');
        }

        $duration = $startTime->diffInSeconds(now());

        $this->line('');
        $this->info('📈 Collection Summary:');
        $this->info("⏱️  Duration: {$duration} seconds");

        if ($dryRun) {
            $this->info("✅ Successful: {$successCount}");
            $this->info("❌ Failed: {$failureCount}");
        } else {
            $this->info("📋 Jobs Queued: {$devices->count()}");
        }

        Log::info('Bandwidth usage collection completed', [
            'devices_processed' => $devices->count(),
            'duration_seconds' => $duration,
            'dry_run' => $dryRun,
        ]);

        return 0;
    }
}
