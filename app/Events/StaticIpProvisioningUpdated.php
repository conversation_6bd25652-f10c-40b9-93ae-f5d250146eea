<?php

namespace App\Events;

use App\Models\Services\StaticIpService;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class StaticIpProvisioningUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $service;
    public $eventType;
    public $data;

    /**
     * Create a new event instance.
     */
    public function __construct(StaticIpService $service, string $eventType = 'progress', array $data = [])
    {
        $this->service = $service;
        $this->eventType = $eventType;
        $this->data = $data;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('static-ip-provisioning.' . $this->service->id),
            new PresenceChannel('static-ip-dashboard'), // For admin overview
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'provisioning.updated';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'service_id' => $this->service->id,
            'event_type' => $this->eventType,
            'provisioning_status' => $this->service->provisioning_status,
            'provisioning_stage' => $this->service->provisioning_stage,
            'provisioning_progress' => $this->service->provisioning_progress,
            'provisioning_error' => $this->service->provisioning_error,
            'provisioning_started_at' => $this->service->provisioning_started_at?->toISOString(),
            'provisioning_completed_at' => $this->service->provisioning_completed_at?->toISOString(),
            'provisioning_attempts' => $this->service->provisioning_attempts,
            'customer_name' => $this->service->customer->name,
            'ip_address' => $this->service->ip_address,
            'timestamp' => now()->toISOString(),
            'additional_data' => $this->data,
        ];
    }

    /**
     * Determine if this event should broadcast.
     */
    public function shouldBroadcast(): bool
    {
        // Always broadcast provisioning updates
        return true;
    }
}
