#!/bin/bash

# ISP Management System Installer
# This script sets up the ISP Management System for a new provider

set -e

echo "🚀 ISP Management System Installer"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Check system requirements
print_status "Checking system requirements..."

# Check for required commands
command -v docker >/dev/null 2>&1 || { print_error "Docker is required but not installed. Please install Docker first."; exit 1; }
command -v docker-compose >/dev/null 2>&1 || { print_error "Docker Compose is required but not installed."; exit 1; }
command -v git >/dev/null 2>&1 || { print_error "Git is required but not installed."; exit 1; }

print_status "✅ System requirements met"

# Get ISP provider information
echo ""
echo "📋 ISP Provider Setup"
echo "===================="

read -p "Enter your ISP company name: " ISP_NAME
read -p "Enter your domain (e.g., myisp.com): " DOMAIN
read -p "Enter admin email: " ADMIN_EMAIL
read -p "Enter database name (default: isp_management): " DB_NAME
DB_NAME=${DB_NAME:-isp_management}

# Generate secure passwords
print_status "Generating secure passwords..."
DB_PASSWORD=$(openssl rand -base64 32)
APP_KEY=$(php artisan key:generate --show 2>/dev/null || echo "base64:$(openssl rand -base64 32)")
JWT_SECRET=$(openssl rand -base64 64)

# Create .env file
print_status "Creating environment configuration..."
cp .env.example .env

# Update .env with user inputs
sed -i "s/APP_NAME=.*/APP_NAME=\"$ISP_NAME\"/" .env
sed -i "s/APP_URL=.*/APP_URL=https:\/\/$DOMAIN/" .env
sed -i "s/APP_KEY=.*/APP_KEY=$APP_KEY/" .env
sed -i "s/DB_DATABASE=.*/DB_DATABASE=$DB_NAME/" .env
sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASSWORD/" .env
sed -i "s/MAIL_FROM_ADDRESS=.*/MAIL_FROM_ADDRESS=$ADMIN_EMAIL/" .env

# Create docker-compose override for this installation
print_status "Creating Docker configuration..."
cat > docker-compose.override.yml << EOF
version: '3.8'
services:
  app:
    environment:
      - ISP_NAME=$ISP_NAME
      - DOMAIN=$DOMAIN
  
  nginx:
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl
    environment:
      - DOMAIN=$DOMAIN

  postgres:
    environment:
      - POSTGRES_DB=$DB_NAME
      - POSTGRES_PASSWORD=$DB_PASSWORD
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups

volumes:
  postgres_data:
EOF

# Install dependencies and setup
print_status "Installing application dependencies..."
docker-compose run --rm app composer install --no-dev --optimize-autoloader

print_status "Setting up database..."
docker-compose up -d postgres redis
sleep 10  # Wait for postgres to be ready

docker-compose run --rm app php artisan migrate --force
docker-compose run --rm app php artisan db:seed --force

# Create admin user
print_status "Creating admin user..."
docker-compose run --rm app php artisan make:admin-user \
    --name="$ISP_NAME Admin" \
    --email="$ADMIN_EMAIL" \
    --password="admin123" \
    --force

# Start all services
print_status "Starting all services..."
docker-compose up -d

# Wait for services to be ready
print_status "Waiting for services to start..."
sleep 30

# Final setup
docker-compose exec app php artisan storage:link
docker-compose exec app php artisan config:cache
docker-compose exec app php artisan route:cache
docker-compose exec app php artisan view:cache

echo ""
echo "🎉 Installation Complete!"
echo "========================"
echo ""
echo -e "${GREEN}Your ISP Management System is ready!${NC}"
echo ""
echo "📋 Installation Summary:"
echo "  • Company: $ISP_NAME"
echo "  • Domain: $DOMAIN"
echo "  • Database: $DB_NAME"
echo "  • Admin Email: $ADMIN_EMAIL"
echo ""
echo "🔐 Admin Login:"
echo "  • URL: http://$DOMAIN (or http://localhost if testing)"
echo "  • Email: $ADMIN_EMAIL"
echo "  • Password: admin123"
echo ""
echo -e "${YELLOW}⚠️  IMPORTANT SECURITY STEPS:${NC}"
echo "1. Change the admin password immediately after login"
echo "2. Set up SSL certificates for production"
echo "3. Configure your firewall"
echo "4. Set up regular backups"
echo ""
echo "📚 Next Steps:"
echo "  • Read docs/setup-guide.md for detailed configuration"
echo "  • Configure your MikroTik devices"
echo "  • Set up your bandwidth plans"
echo "  • Import your existing customers"
echo ""
echo "🆘 Support:"
echo "  • Documentation: docs/"
echo "  • Issues: Create GitHub issue in your private repo"
echo ""
